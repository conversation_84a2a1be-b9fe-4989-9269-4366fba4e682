{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98027086a0a91cc749df31db074cfafe36", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f0c6583cbfac7bc80c500236f59258f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9819d68cf8abfc37da4c4738d9448758f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989f71efb1396aab0501a59f707d927c9a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9819d68cf8abfc37da4c4738d9448758f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f47cd1733b357fa51833bf87ba9d0cb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9862d3534eb10f345e0415e1b6e8f83251", "guid": "bfdfe7dc352907fc980b868725387e98d3c21dca6bcbf12a275bffb4d72d34f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983037b5d5dfd92581186e7c2219439555", "guid": "bfdfe7dc352907fc980b868725387e98f7ec2cbea2fa2b87993fbf671f8af7e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b526f22c4cf4c4bd6986a0d99c231030", "guid": "bfdfe7dc352907fc980b868725387e982f033c0fa84c68fe5321ed7589f616c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8eb43c4a00bdba2d271244ace6d3608", "guid": "bfdfe7dc352907fc980b868725387e98e20a7a82bbb153b6c450dd5e53dce375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8dd6fe1a925c3f64c524a1d7efc5e78", "guid": "bfdfe7dc352907fc980b868725387e98a9064a995abbe07e23f1a6313866e378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f929929538f80f33d9348fa900048de5", "guid": "bfdfe7dc352907fc980b868725387e98fa9e787d6c4d212db8b578b94b980119"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a1bb2d86e0dc495dad9567ea325c52", "guid": "bfdfe7dc352907fc980b868725387e9895ef85d3d2aa1908e82f6204b1ee632c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3c12fa7e707a1116546371e8052d2b8", "guid": "bfdfe7dc352907fc980b868725387e987c0c9edf4c636001b48c06748d8516da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987410ecf5e371a501b63cd308d6f454da", "guid": "bfdfe7dc352907fc980b868725387e98cd8adc101182362bfa411e78f66b4bd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70017089e234cee622aa8ccd6c69ffc", "guid": "bfdfe7dc352907fc980b868725387e9831db42c633814fc701185fa60b324c57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842cdffb768657780c29905f5a0170716", "guid": "bfdfe7dc352907fc980b868725387e981993a28f2488a0ea79ab1d5850614667", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d78944b5fb01a88663b2100edd1a3e1", "guid": "bfdfe7dc352907fc980b868725387e9863c9f6deb3dcabdaa07c6c8f4b24a806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb6288528acbda10f44e691a0c34d76", "guid": "bfdfe7dc352907fc980b868725387e9870471f20c7ae1f3b844b78fe5c61235d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22f1d36f4578c2d0ca4e922dabdd277", "guid": "bfdfe7dc352907fc980b868725387e98804f7b893d745590c67c22a65c948392"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d451f48c79600c463b3eb91b352a81fe", "guid": "bfdfe7dc352907fc980b868725387e98ce87266aabdaff8a09f0b0e41e218f5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af07accad84f11ffc0d91d2f01490201", "guid": "bfdfe7dc352907fc980b868725387e98ec5ebdd69ef7bb889fbb0413246dba51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b4cd15b4a262276ea1325b271915f6e", "guid": "bfdfe7dc352907fc980b868725387e981c6b4cdf5e755543c74ab309bdf0363b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98733ac1b862b19045c6bc6bc2b3a7b0cb", "guid": "bfdfe7dc352907fc980b868725387e983182323f9127363330d9d12f4abe5fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8df6f34852573c55915652c1ebfab36", "guid": "bfdfe7dc352907fc980b868725387e988cb5ca0453a2e45a1e93ab3a0c290e7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1bd707eda9038672bbb521f54ce9725", "guid": "bfdfe7dc352907fc980b868725387e985823a3bfb044ed4a1540ba2336906853"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc25e7c89eee28508b3a5af41948239", "guid": "bfdfe7dc352907fc980b868725387e98d0b9ebe54f5e5081a6514520985c5ef4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d88019211758474cd6b3c28bc08cf4e2", "guid": "bfdfe7dc352907fc980b868725387e98d2fabf2ef622f98ab8b6977b3b97e819"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf179bc17059b747e270dd2b1834bd7d", "guid": "bfdfe7dc352907fc980b868725387e983244de329d103b359a126ac0c3599ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983339b75e48f098546722958fce8abe7d", "guid": "bfdfe7dc352907fc980b868725387e98d1e1a961289c4cf856c92c6d477a3a63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d11d1c1cb03767f0c7ac0893a6ed91bf", "guid": "bfdfe7dc352907fc980b868725387e982b29b77c4d40b8858cfe1ce0798def58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f73cad4c989f28ca2f22727947597667", "guid": "bfdfe7dc352907fc980b868725387e986011fe9b34483e7b024df692d8c240a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848a765c9f46feefd017ab31b25db06d1", "guid": "bfdfe7dc352907fc980b868725387e98e6c395b1d9a64e7b83d312636c198f4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983216e1777c37d52a352a8cb6cc608b3c", "guid": "bfdfe7dc352907fc980b868725387e980f24ad685e010d6cace01489e5484124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4183ebf41f757048748fd4c7eb52e3b", "guid": "bfdfe7dc352907fc980b868725387e9808da944960fd77d03f39dfc57ad0a357"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a349a668baca78ce47f752150a237029", "guid": "bfdfe7dc352907fc980b868725387e98d89383e4cc351ab2f24d66e5828735fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820939d60e06f777de436bde912dd4549", "guid": "bfdfe7dc352907fc980b868725387e980cea824ea5bc84346194d7000c4a7fab"}], "guid": "bfdfe7dc352907fc980b868725387e98199258ce5e843dfc5532147ae2ed238f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b9c0903f7173cbb41b93ee9ba79b6435", "guid": "bfdfe7dc352907fc980b868725387e9825cebd4c8abb310acb4115c6e3a083a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834dc014147202559fffceeb996edfa3f", "guid": "bfdfe7dc352907fc980b868725387e98ca3a045b0494c9a454d6e15a231db3cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad2ae90a8a45c7e03e49d42bd41cdfb8", "guid": "bfdfe7dc352907fc980b868725387e98dd53cde37495c784ffa338cf3758161d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6d65362fa2cf0f0aec3f6332222b43", "guid": "bfdfe7dc352907fc980b868725387e988ba7748521d5d7abe443f0802e8e8b24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988202df6026927e4d6021457112b61830", "guid": "bfdfe7dc352907fc980b868725387e982a2d303e598cc2ac875e30f52a41c596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6bd25fb661384436cb6dac23e75c529", "guid": "bfdfe7dc352907fc980b868725387e98b82baa039a77bc936f4addec1817f0c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af430ede085d84f9bd1497ce1f8e1fb5", "guid": "bfdfe7dc352907fc980b868725387e98ccf3dfbb5e466fba6233648269bbe9cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dc2042e28b43ca30f9f84e1d24dea0b", "guid": "bfdfe7dc352907fc980b868725387e98ac55772d8dea8887067b4ce9cf1e34ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98396ea6cdefe5cce701709dffc54be193", "guid": "bfdfe7dc352907fc980b868725387e98d06f7fee50fcbae91f4d09da3a3fa119"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980077f02a6f9c119795f5b0b1222f891a", "guid": "bfdfe7dc352907fc980b868725387e987f99b31641a03d92a8097d59463cc86d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2cc2c540f93cd11bec5bd309301137", "guid": "bfdfe7dc352907fc980b868725387e9894e399303856197a52f9864fb55b98f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac4e55b51a598bd5747b9cb553e0788", "guid": "bfdfe7dc352907fc980b868725387e984052c0aabbe72a142f447d4da3972634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98856c737a4e5349aad149fd6dd1cd206f", "guid": "bfdfe7dc352907fc980b868725387e9894aa82cbe469c0a558cc53f7c211ed1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38fd110591a9662511a1bb2d8b62486", "guid": "bfdfe7dc352907fc980b868725387e982a4a0e89856a5b365da4c6675ad171e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426f76d1bdbca6071cfe23e0c48a349d", "guid": "bfdfe7dc352907fc980b868725387e988a43cdfedd12be6c92893ca8790d40e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ce980bf64b3047ac12379e2d5f971e6", "guid": "bfdfe7dc352907fc980b868725387e986af40349abad28ca405f7a47b741070e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a100ebd4e5ee506cd3b7b77481afeab3", "guid": "bfdfe7dc352907fc980b868725387e988b5eda4fc0be440a3dbc84ddc613779f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98635f30b5cd0846923c972f55de5ac7df", "guid": "bfdfe7dc352907fc980b868725387e98d1a127c47551d2bfb219e767990a2ab1"}], "guid": "bfdfe7dc352907fc980b868725387e98e519d1ef9eb8ef9045df4c3e4e0a79d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e986810efe8726e97bbf1d1471c7dc5091c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870350cda99093f910f2f9eecd3826fd9", "guid": "bfdfe7dc352907fc980b868725387e98a40492a95e7be6d381fcf487895e7555"}], "guid": "bfdfe7dc352907fc980b868725387e98bd236787c00587b404a715bf3377a423", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984f501952bb4da137353d56fead4ec92a", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98b771b7b2d9577cdd8deef022c6a9059c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}