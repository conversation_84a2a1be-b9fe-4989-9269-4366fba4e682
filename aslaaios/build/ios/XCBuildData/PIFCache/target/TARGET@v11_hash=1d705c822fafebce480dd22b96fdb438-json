{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986de28c4f85ffec74c78801fb89bc7d30", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9875818126485ca400f0d34afbc0fc4a33", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9875818126485ca400f0d34afbc0fc4a33", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d83223130e30085ed889e54faa218a44", "guid": "bfdfe7dc352907fc980b868725387e987f28047e02a7d34a46f8ada17b829c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98122b04258e753c49a3d425053fea1cae", "guid": "bfdfe7dc352907fc980b868725387e98f6c67c4679e7f7927d29cc6777b4854d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98307fa34b98ed49e02ef15116dcf6cfa0", "guid": "bfdfe7dc352907fc980b868725387e98fcb7e61931146dcd711e99248d6ccb4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983357a8c6dc18a42a2c4527dd54342bbc", "guid": "bfdfe7dc352907fc980b868725387e9895fe4b33f65c965da7dc45a450744ad5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983576b76b5a935472aba8ed9f94a0f762", "guid": "bfdfe7dc352907fc980b868725387e9881f94adb811a31f238513440b73eb9ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd89098739aaa6d4e9fb5d52320694f", "guid": "bfdfe7dc352907fc980b868725387e98d2993dc352971d9dec3337f6492eedd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c30f76ff5ded289d45871c38d13361c", "guid": "bfdfe7dc352907fc980b868725387e987744d4bc1141e8c3af7814a94ab5d191", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a00c607bed99df7ea29b05f77f9066", "guid": "bfdfe7dc352907fc980b868725387e98d44ce0ad517ce468c11abe8a1a7ee842", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2678acf359d179cd4353295458e972f", "guid": "bfdfe7dc352907fc980b868725387e986dfc835f86ba61de853986e0c332c9bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd748136a33625d57b6099cccebc97b", "guid": "bfdfe7dc352907fc980b868725387e98832bcfeadec19c9672e7d13bba695944", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d8f63ebd020103ac3649db004a4ac03", "guid": "bfdfe7dc352907fc980b868725387e987522b569e1888c59443fabbfd9eec3d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855f2389606538b815aab54eadb601b36", "guid": "bfdfe7dc352907fc980b868725387e9826df2a365ab1701d04b81b250e704469", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f8e44f6db36d056fe48de072ad2c39", "guid": "bfdfe7dc352907fc980b868725387e98683700ed014e2584117d9919dc247bfe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875c0b1c6b8f60973a791209f8c9f17e7", "guid": "bfdfe7dc352907fc980b868725387e98a95b46a3d3c80a8cb5734bbb7717809c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22eade7c29f9bf763e0acc571917859", "guid": "bfdfe7dc352907fc980b868725387e98c814b053e6b465d54fefe875995c908b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f08879be0ab80e9fced49fd897b02742", "guid": "bfdfe7dc352907fc980b868725387e980e4020bf220b21c849a1247a598ef500", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802c48c51a838932b8052706656a1645f", "guid": "bfdfe7dc352907fc980b868725387e9867e1d59733a195924e43c7221ab3b507", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c6c574f52e5b9a0f0fd974d43bb19fc", "guid": "bfdfe7dc352907fc980b868725387e9836a664f9634f39c09eb263d32eb33de8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98794f7a265d6214e44a14bcefd9f1dde6", "guid": "bfdfe7dc352907fc980b868725387e981012013b7c46227b76d2a26c3973c5f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987967c66444f7289708a8eadcddc15387", "guid": "bfdfe7dc352907fc980b868725387e986adce0f63de9503184a2571c68ae8037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4010cdc3600fbf5f0b8d2c90e4ff91b", "guid": "bfdfe7dc352907fc980b868725387e98c97111306fd51e26ac236698d182d1fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f33ca2c96141bb8a3a69a2fa7f3550", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98859902834b96ac542cddfc1e6876b022", "guid": "bfdfe7dc352907fc980b868725387e98c97bce089e46629de083eac4838dd800"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ef3478f8f9520009ac4956cb4252ef6", "guid": "bfdfe7dc352907fc980b868725387e985817ad63c8e4f8db81700ac14cc618cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac93b395cea5df7f67d350c77f469a5", "guid": "bfdfe7dc352907fc980b868725387e986e494a42d2c0843cec709c09ffdc69a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f5196ed9106eb80fa766eb99548e8c5", "guid": "bfdfe7dc352907fc980b868725387e9805c8314d750be278604bc8677fcf4420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984978cb05fcdf58acc7ee0cb0145df4b0", "guid": "bfdfe7dc352907fc980b868725387e988270e34481adcde5ab4b6a9daa89a011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881b12f864169bbf324785b6a56291750", "guid": "bfdfe7dc352907fc980b868725387e98615ad6c8a17ee1f04050629a5a2a260a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d503ff075ebd012961f2d148b411dcdc", "guid": "bfdfe7dc352907fc980b868725387e98fd9db82003eb1b08fd6976b7ba6920aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e9c2974d64625d38db6bb00f0850ab8", "guid": "bfdfe7dc352907fc980b868725387e9836872f22a07d006d803b66800b0fd898"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0cd089ab107db5f44352a6800b46b23", "guid": "bfdfe7dc352907fc980b868725387e985cf4803afdb0ccdba56bf5e79afdbebc"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}