{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9845b6c9f2ed7334efbe7153e6fa189da0", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cd80ed97e3579cfc64bf14521121f1f7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986073180e690f94e4bda9fa6ec25f4d20", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983c208cc7791be2b6bce29fb991f5890d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986073180e690f94e4bda9fa6ec25f4d20", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824ab6c468ab7ca83f69fa46ac9895e36", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8ac58af51e9296f254e4deac9a6332d", "guid": "bfdfe7dc352907fc980b868725387e98841de399c8bf2d9f11ce27088d8f8f4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7f40a72ea937aeaf20e83dac9ca39d1", "guid": "bfdfe7dc352907fc980b868725387e98f1968321caa7a7dc4bdf998d3054d24d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986066359470d33b4c913375c506bd0586", "guid": "bfdfe7dc352907fc980b868725387e98940a94590a5ea84fea75c2af3687c03d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98996652529d4e35ef7382c631f1081693", "guid": "bfdfe7dc352907fc980b868725387e98924220c6a3b8135ff939c08a80bd7cce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d100f3d94a6bee88c3a85d225c7e5fa9", "guid": "bfdfe7dc352907fc980b868725387e9802b56e7c3b6f3bb7a8637523531b9093", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5f34964863b8bc8c8790aa0d238490a", "guid": "bfdfe7dc352907fc980b868725387e9853bcf1233a58febd04af28913d359749", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b26b82f6132757f7ac40473e0aa5ba5a", "guid": "bfdfe7dc352907fc980b868725387e981c99c9a1268b21652d21e652b8347db2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6880d7aeb857c1ea9046c308427600", "guid": "bfdfe7dc352907fc980b868725387e98e900b8d4a6cef2d7dc8fa07cc02cd6ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b040af088d2e5292d5d104d6b8b2b18", "guid": "bfdfe7dc352907fc980b868725387e9826b2ce729f9635acfc95a679b44dda95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a706dc752de14e9830e0802116bfb26c", "guid": "bfdfe7dc352907fc980b868725387e9856478e0b68a4eca8f901025f55c4cc97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981747bf6277d31adbd1e81d1a2f948b3a", "guid": "bfdfe7dc352907fc980b868725387e98675dd756ad17b121bbf29955ce17071b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98374fd14452808ed90ccbeb33bb66f984", "guid": "bfdfe7dc352907fc980b868725387e98fada0c6301d66581047a60e996079cb8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c606755a20b57fb780d9ef3412464b9", "guid": "bfdfe7dc352907fc980b868725387e98ba78db40b4f5f46997da4c60dee03321", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af88911d72dc2cecd0cdde47628af625", "guid": "bfdfe7dc352907fc980b868725387e9877da08a92c30a4af828f4355528fa60f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842e05bd23e86424a4076d29b215e1181", "guid": "bfdfe7dc352907fc980b868725387e98310f7eb1793edb09cfd70a7a8b88d26c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c6f7f9d7a95fae921b2b3ca59394a1", "guid": "bfdfe7dc352907fc980b868725387e982744f7f5cd967a783198fe4f01fd43e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ec53c290154e33d064f866b25b8cb0", "guid": "bfdfe7dc352907fc980b868725387e984109194713e827853c28446c0b0f937c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8429db79ac6a327e07a368a4a3a0f9d", "guid": "bfdfe7dc352907fc980b868725387e98a315ce3c3d588a7527f2131769a7b8e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986012d73eb986e5a1aaa7952a9caeae59", "guid": "bfdfe7dc352907fc980b868725387e98891ff1dcf537e545f532c077b14c4100", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861a2f55ed8c6ada045fa8f0d5d31b712", "guid": "bfdfe7dc352907fc980b868725387e9862e637dff9b5f8cb43be8290fb8c9cb2", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b35f2140c1908280a190e037d164935", "guid": "bfdfe7dc352907fc980b868725387e98c55f59e3c7ffffc88fa88d66e6be5810", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9a0bfafe21945a4d8dffe525e1cc2ac", "guid": "bfdfe7dc352907fc980b868725387e9887a9dac162ec38da0ca1a4a52f07037b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98612f6f201b811bb312c713ce315bbbe5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984815821345749f68659171496e4cd9c6", "guid": "bfdfe7dc352907fc980b868725387e98a6de28999ce7f37cdb25fbf1d17344d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98040ffa5573a6bd0cd5cfd4af8863193c", "guid": "bfdfe7dc352907fc980b868725387e98441ba93116bcc58f7cdd489de9556c87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69eecd79d44fdd1a64239a96fdc84e8", "guid": "bfdfe7dc352907fc980b868725387e98e85a246f26ddece4552ff1e930c4313c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5a87603428fae90f8afe9e4f56e0af3", "guid": "bfdfe7dc352907fc980b868725387e985ee61a09ca54d38af623a85e10b5909d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875c38348923e37e3d157eb2803c4cf76", "guid": "bfdfe7dc352907fc980b868725387e98599f2ba59c041976fcecdcdaafaa282b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce3f34fe3108ed4f00260c92455f57b6", "guid": "bfdfe7dc352907fc980b868725387e9815d5eec0d2af8193e374eb1e36a2a8af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b03c3f0959d335e519fc792467a83ae4", "guid": "bfdfe7dc352907fc980b868725387e98bbe48255bf4ef88f6ff7f785ae782ecc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d832cda6af51aa1e2092abcf13f27f7", "guid": "bfdfe7dc352907fc980b868725387e985d0f4975100e2cf73634ab34bb7710e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b94edb4af824870b15c4061e76e4a07", "guid": "bfdfe7dc352907fc980b868725387e98b556d105cfdf382636c9e51813ce778a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886b1f61052055847698fefbef8f19993", "guid": "bfdfe7dc352907fc980b868725387e98eca1ceb37fbc499599570da8c0066ab4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8daaabda770f0cc7e817bd386febd90", "guid": "bfdfe7dc352907fc980b868725387e98fc6bef97a35a9763a40af2f76ad3cc8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4cc7dfb434bf5720b7bccddfeec6514", "guid": "bfdfe7dc352907fc980b868725387e9867498a47de5ece9908d5437b2f0cfceb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae73c38083ef406b68b6cdd426369f96", "guid": "bfdfe7dc352907fc980b868725387e985e93eec43897bb4d25fcd248c274b8fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c3aefae2b9a4417440f67639695df6", "guid": "bfdfe7dc352907fc980b868725387e98fe8e08e4d22b615ce862d8b1753b8776"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d034dee23876b09451b4f6457f5499a", "guid": "bfdfe7dc352907fc980b868725387e98347a1053b619b81a1bcf8db530b6d50c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98161b2d9b33aeddccde02b6279859ace1", "guid": "bfdfe7dc352907fc980b868725387e98d6f0b0d0b4396bb28d1d1d1fec7b06ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b997faebb37a3dbfec73149252b06f1", "guid": "bfdfe7dc352907fc980b868725387e9825d4a2fe6fa46cb4bd44478a2ecbfefb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827969122ecb121246400284b77aac2b2", "guid": "bfdfe7dc352907fc980b868725387e98bf592684f520edc509c309cc94db0a03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987117a696df478216ae92c82940acb1f2", "guid": "bfdfe7dc352907fc980b868725387e98b9cf7a992a5b94eb77f07926b6baf9cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a22e2851a0f20411ac6fa32ecfe3365", "guid": "bfdfe7dc352907fc980b868725387e98f3042c74e51f5b2725e76f0736ca4f44"}], "guid": "bfdfe7dc352907fc980b868725387e988e1580acdf3ed762fb86974a6aaac6d7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98072d239f12045cb5ed18e9e59a882a27"}], "guid": "bfdfe7dc352907fc980b868725387e98605b45b0433d8352e0a0420885acddc5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984b6d1e82963a5c05636aa85c6550cdd6", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98bdbae37892452c855fe65bcdb62af8b0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}