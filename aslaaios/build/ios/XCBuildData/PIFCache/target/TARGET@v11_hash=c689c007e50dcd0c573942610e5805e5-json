{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98296f23ee20c0802519a657f6a7990e47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound/flutter_sound-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound/flutter_sound-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_sound/flutter_sound.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_sound", "PRODUCT_NAME": "flutter_sound", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b9b81931b66b864ce056eac4a63bbfc", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9815960c9e11599ec483846fdc2f57ab67", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound/flutter_sound-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound/flutter_sound-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_sound/flutter_sound.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_sound", "PRODUCT_NAME": "flutter_sound", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5d5b2097e63da9f2dd219c1a01902c8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9815960c9e11599ec483846fdc2f57ab67", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound/flutter_sound-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound/flutter_sound-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_sound/flutter_sound.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_sound", "PRODUCT_NAME": "flutter_sound", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988b37a8743ec9e375ed207bca0624fd6d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f412720450e48eba510204747b426279", "guid": "bfdfe7dc352907fc980b868725387e98a4709156afa699f8d1b51b1df28c99e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b8054aca87510358bcf06d17140b97b", "guid": "bfdfe7dc352907fc980b868725387e98e7025b23e0c7773a1ef89ad3bba99d17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9f8b70c1e471703ec0f88a122712283", "guid": "bfdfe7dc352907fc980b868725387e9879524c9ac88c8416c5ee92355b54aebb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e9bd584e87f27c1c615cab60ca6d821", "guid": "bfdfe7dc352907fc980b868725387e98d872a81924295be4a36141734870d23a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e59d896bb36642303c271c06501dc95a", "guid": "bfdfe7dc352907fc980b868725387e98f7cee12f06a53ad967adfc6bd1f56c17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98032f7d85fc041a50a947aebb75bfd1c1", "guid": "bfdfe7dc352907fc980b868725387e98be70d9a10e44ef8d0c081526871d676a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864057d05753394bb571074e9c5d368dd", "guid": "bfdfe7dc352907fc980b868725387e980bf2931318372d943f7f605b90c66667", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9aeef718e3c6fb834ee80458e83a644", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b83a13bf503f1b943e8b741519e799b1", "guid": "bfdfe7dc352907fc980b868725387e9824c29edd0de71ef70b6981a2936e1306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98266e518f0477e8d67a7d943f86a3e187", "guid": "bfdfe7dc352907fc980b868725387e98446ebcd239373f2861676a6255b09486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fbe12d246f563929cfe08836a588e2c", "guid": "bfdfe7dc352907fc980b868725387e985e61a3f07e1359964a40a6523bd0e78d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989feb7e536c4abc20fcb6fd8cc777f4c4", "guid": "bfdfe7dc352907fc980b868725387e98c6a09489955c5a4c3ee1b13673da7fbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c345d794b3a894e9233f2e978c716c0", "guid": "bfdfe7dc352907fc980b868725387e988f76398b1f0cad5913cd26c6ed59c83f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5e4ad8718596f5c29a91c8b7bd89359", "guid": "bfdfe7dc352907fc980b868725387e980c1dbe52f1c7a314ea283f59c75df3a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cea76207fdd6ad4192035e9fb3ca747", "guid": "bfdfe7dc352907fc980b868725387e98d597bf8a32e7570cdeafd9e87a17a5be"}], "guid": "bfdfe7dc352907fc980b868725387e984033afda0da5bac1f211c6105dad6a37", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e985126fee2528077bb1e3e71371f705b75"}], "guid": "bfdfe7dc352907fc980b868725387e989cb9d4962ca46483a74e755bd7837e55", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9817e24f9e354470314dfab56b635e96f4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9817d41af66eee3a4c1e145e17163b22b8", "name": "flutter_sound_core"}], "guid": "bfdfe7dc352907fc980b868725387e988e2765468126b8189d0a656452a5242d", "name": "flutter_sound", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a792d892ce1319f30820f36c4757210b", "name": "flutter_sound.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}