{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb311003c0664f93374ea5cdea2f6867", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838b346c994131df87de18dc6d14cdca4", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838b346c994131df87de18dc6d14cdca4", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ea7e3c2e774c8d4737ab917365f24f80", "guid": "bfdfe7dc352907fc980b868725387e982ef32830090a5f180689e16980928fd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6c8ee0dfe094e56358e816785a769ac", "guid": "bfdfe7dc352907fc980b868725387e985a916ced4ab8bf3396d2522efbf655a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdd7749f51675d3d8075388ab97f25dc", "guid": "bfdfe7dc352907fc980b868725387e98ff53f399fa1e2ecb7849daf4286e9ade", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2c53c3176fed63a168a9ccc536535c0", "guid": "bfdfe7dc352907fc980b868725387e988ad8e0322ec48311f0d9e5a75f54c724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f702e9d273fa90e7199a4a7b89070e4", "guid": "bfdfe7dc352907fc980b868725387e98b11ed937bba18c7408a49a9a028bb33f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fafa09e095b291b7b2f53879edd873d", "guid": "bfdfe7dc352907fc980b868725387e983d80b84cb41fbc5fdcc8bc3d451f292d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ce4db9b5b8b6e8652ecea81ab5a3de", "guid": "bfdfe7dc352907fc980b868725387e983bf5db94459714fd2562bfa5ba3794e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983df74122d6e19a9accb9cad1659bd7d5", "guid": "bfdfe7dc352907fc980b868725387e98b4cf2c1398499b177aa5f12416a2cefc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853081884cb7e24327ab4ff099bd8e97f", "guid": "bfdfe7dc352907fc980b868725387e98a5931e111640051bba64bb234ef28a63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b29354327b7260c7d39755fbf30962", "guid": "bfdfe7dc352907fc980b868725387e984e2d3cd6dd0a8b8ce5a9345bf5eed7de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98319c68a9e58f733510f334290f1124a3", "guid": "bfdfe7dc352907fc980b868725387e984bcefc59b1341fdd0e8bad71dba39c92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f7fb8d91729a36562f04c71fcf8591b", "guid": "bfdfe7dc352907fc980b868725387e98c7eb3ea66f1fa44a1f5858283c92137d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575ddfea78192415a9b5fd28635b971a", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0545c5876dbce77b90db88b91c587b5", "guid": "bfdfe7dc352907fc980b868725387e98cdc5844f930b9d0fd5308dd5856a500e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ded8a39532fecbf37ccb0c5ea62d01c", "guid": "bfdfe7dc352907fc980b868725387e988f5bb97be79cc2d8abff7c92bd4b7567", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98687d38ddbe7fb58d5d8dbb153bd3fc79", "guid": "bfdfe7dc352907fc980b868725387e983de4f1e8f5a97a6028a6bb34227f49d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a49449bc2ce5f26a811628353533d116", "guid": "bfdfe7dc352907fc980b868725387e98f2f63511bf606bb48ce3eb1b71b306f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983efd035cd963e36d67c96dc0099aa359", "guid": "bfdfe7dc352907fc980b868725387e98327b38ee62f98da7d09431ea5004a053", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa38011d1e7c5656e832f6487ccbb2b", "guid": "bfdfe7dc352907fc980b868725387e980188baa19b7914aff2e34c8da54773e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833fb5112d16dddcf3f4733db7d5d7469", "guid": "bfdfe7dc352907fc980b868725387e9812d25da0977d54b4f93c43814429ebf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875ffa514bacc5a72e682d4fc6f4fe313", "guid": "bfdfe7dc352907fc980b868725387e98b9ff284581573c4ba830aef19b6cb271", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a17ebf3449c27d256dc240f760ba567", "guid": "bfdfe7dc352907fc980b868725387e98938189fe00ada6ed049fb839ef6bf7d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab90031f2f0196504a6665b42b0dd4cd", "guid": "bfdfe7dc352907fc980b868725387e9864333a3fadcb2636b3f7fea467c141f6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98481510a82f147d0ec1c44408859ea51e", "guid": "bfdfe7dc352907fc980b868725387e982a61503d8185fa65d0d61935791efb04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d25f23f689096bc02aaa5f49ca0ce9cb", "guid": "bfdfe7dc352907fc980b868725387e9835220589345efb92af42631dbf6b9459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f3905d0d9ad014836b8b40ff74fedc2", "guid": "bfdfe7dc352907fc980b868725387e98c4ed4b65682f6200b53ab06f33e7c896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5416a032a12646176542257642911e1", "guid": "bfdfe7dc352907fc980b868725387e98f67d51cdaaf5e1f78be421f374efde62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f21542e29e5bb7bb8bd2c0b13a75239", "guid": "bfdfe7dc352907fc980b868725387e98aa97ddbe032d174d7e0e5ddbc64b9d5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984616db0100ed3c28191c6df9d13669c6", "guid": "bfdfe7dc352907fc980b868725387e98ff8928328841298b0ed62804c598770b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b8e917c90b452739fddf31c84bc42c2", "guid": "bfdfe7dc352907fc980b868725387e98ef84c0ffd43efae0c864668786839def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98083d4ab23d9265c774919c6403916fa9", "guid": "bfdfe7dc352907fc980b868725387e9848cadd1ba7bb24a85b533aabf8e22799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98884e7cfa53fa96f99bbf28d706dbb270", "guid": "bfdfe7dc352907fc980b868725387e9821497750e138db752421f5224d128654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862d8b05d872abfecb89300dc376c50af", "guid": "bfdfe7dc352907fc980b868725387e984a4a9db5329a7711e351ab57794c53a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dfefd5205434ca9b0850d0c844324ac", "guid": "bfdfe7dc352907fc980b868725387e9833885db617a4d69d1571730987fbd3fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98270062434e9e4a93c0c2ceeec138e13a", "guid": "bfdfe7dc352907fc980b868725387e9824cbbfe0ddc5f68e6717f61ca670184f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc77f649eb4fcf4e5dd6b147e4752ca5", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e631255aec8c283920b2a4616069ca9", "guid": "bfdfe7dc352907fc980b868725387e984e5371b8cd4eca1a3ea6dfcf7d307c37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98981ce40e769be505f7cd253ccc63abb9", "guid": "bfdfe7dc352907fc980b868725387e98d3572e24141325a30bc3c0a31c41fa85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60fd8a1db161de238bef966274eff26", "guid": "bfdfe7dc352907fc980b868725387e98b2f53712d1d0af9bc7d13919388a2c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820aade4023746d015929f794ef5d7b1a", "guid": "bfdfe7dc352907fc980b868725387e98e29bf2527a03f8a7f0ee4f8fb682157b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800003312febc07e79085bb3fbca9b240", "guid": "bfdfe7dc352907fc980b868725387e9827d1f3d71d300da27dcd08b0f8c4d523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877be54a09b120a1177c9c5b8771746b4", "guid": "bfdfe7dc352907fc980b868725387e98eee17b6918192a58e3242e8202b1af3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc7ea1649fdcb300dc129811536d81e3", "guid": "bfdfe7dc352907fc980b868725387e98319f0ee3ee4a0d94236a327f7214191b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98202c0f55a6ed409059bec5dd6dfb79eb", "guid": "bfdfe7dc352907fc980b868725387e9889d6808f96db31c0fa88901646d50905"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}