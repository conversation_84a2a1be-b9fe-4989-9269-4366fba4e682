{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c82ef84b5f04107aff549dd3dc15c932", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9843cd48fc5c4edb7e03a02fcacf0732d6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98074ae605185777d2f7d3972c63aa97b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b3111d6f814da9c155c0bc8a00bcfef", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98074ae605185777d2f7d3972c63aa97b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985b852a54d6fb9b306b14a3ba5481099a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980ee423265dae3e2b5d7e47ebcc016020", "guid": "bfdfe7dc352907fc980b868725387e983cc98b78a8486a76a730230fbee4aa4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da4a07b23bb52c6788e25b106b7068ad", "guid": "bfdfe7dc352907fc980b868725387e98ec20d652cb45d4250569441be1456ce5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cb3e0e5de3db0ef9f7bd543a251d40ae", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c132f82c4f15847edea779f4c21a28d3", "guid": "bfdfe7dc352907fc980b868725387e983ef0387d6417242b44f0d8524e357c2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b687d1135d32cda1b6dd13b0e5e4fa50", "guid": "bfdfe7dc352907fc980b868725387e980afb48aa973838e5054e9a463098639a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899d79f2ec13498105b22b0c31e79374c", "guid": "bfdfe7dc352907fc980b868725387e9850b26f36612a866f7a3fb887a9c2c5f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d1fe198219225a934b13ed1a150f118", "guid": "bfdfe7dc352907fc980b868725387e98dbcb195ee3786efcc597d19478eb8f6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b2fb13155e0bd92d8bc05f9dc322bdb", "guid": "bfdfe7dc352907fc980b868725387e98a7eaa7bfed9eb016144c39946ab56f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b827198b5802c99edbd2442068075b63", "guid": "bfdfe7dc352907fc980b868725387e9897d81eebee888ec413acc3fe75cd18af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c9e3aa414da7da384082dda34a2e5b2", "guid": "bfdfe7dc352907fc980b868725387e9867a1830e820bb60868382cc5016831b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8b46a3297575711e8120cb896e206d3", "guid": "bfdfe7dc352907fc980b868725387e9815d9a9fff5caaa47f523f2ec978dd62e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b6e644b327470722a524b24da09d5af", "guid": "bfdfe7dc352907fc980b868725387e987f275895674a76ae5f7b8a60dc1cadd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98788273bb913fc9c227a09c9d89bc7d33", "guid": "bfdfe7dc352907fc980b868725387e98b5b6be22a7130a08fc40279bc7ffd2d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a1e0f09596966584cf00cc0e7ac32d", "guid": "bfdfe7dc352907fc980b868725387e98df461b2a434f83acfb6d155fc3060507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984574dc6f8f771462ff71e05b3117e3d7", "guid": "bfdfe7dc352907fc980b868725387e9881e19cb7b18d6a8af1a68a36e84cdc2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e52182e780c1e12089e87ebabc23c03d", "guid": "bfdfe7dc352907fc980b868725387e98d6da1299b66c6882da8855a2802d7ae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef1ad2a3a7d80673c165596fe98b9396", "guid": "bfdfe7dc352907fc980b868725387e983a72b83978a89493f3579cd1f82df7ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb10d6aa56659988deac95fba20c32e4", "guid": "bfdfe7dc352907fc980b868725387e987adec1d2775108056340ae1438bb6633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f11633b9a5ea7b1c39e38d2d07f708", "guid": "bfdfe7dc352907fc980b868725387e98d2e78896cd30fc76e0bac8064ecd86b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a79d766c21c1a82a81ef3830ac32b85", "guid": "bfdfe7dc352907fc980b868725387e982eb45d4907d4884a53e8e3b811b19069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc8f2dd9f8d0689f69cd63b9e02ffa59", "guid": "bfdfe7dc352907fc980b868725387e98ed96a287d9a9ff12a3e1c7de8987c039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852823ec9b5ff7b06530400f0ccb4e468", "guid": "bfdfe7dc352907fc980b868725387e98d65c90e2b117336d6bfb7e04ba2f8560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e144816c27d178741337735b39530495", "guid": "bfdfe7dc352907fc980b868725387e987471c817d6e450f12df8f41f442c6529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb608bb9acd3420ef2547e7a425f0151", "guid": "bfdfe7dc352907fc980b868725387e98995ff16c0607b14af00218d95769ef85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa07cc8c90c0c84414a9ed5ffafacfe", "guid": "bfdfe7dc352907fc980b868725387e98f8a7992626d3eb9adb95e2d7525f34cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a56b2cbb8d85dcf4edf8cdb406b7a9", "guid": "bfdfe7dc352907fc980b868725387e984dfb7a5049d8be67b282e4be104d1094"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dddd9f251be0d03a610df4e8bf5473b", "guid": "bfdfe7dc352907fc980b868725387e980046c0729984d0d85b8173f85104c080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e8b0cc2a544d0c9c78cba08f6562ff3", "guid": "bfdfe7dc352907fc980b868725387e98f2a433f4ed3b59fd63e8d20214859c8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ee55cea16860f8525458573469d7bc9", "guid": "bfdfe7dc352907fc980b868725387e98555ac170a3207e0ebb67ed89323f3f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ddf62844012f93c30d186f93eb4aeb7", "guid": "bfdfe7dc352907fc980b868725387e9878d64ffbc32a4c7a857f3f01173a406f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985492e944799208e83287311afe95efb8", "guid": "bfdfe7dc352907fc980b868725387e9810b8dafca9c94868d2cb4d3cb182bd7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848969a4e25b6873e513ad85e5d927df1", "guid": "bfdfe7dc352907fc980b868725387e98a9ece4595116d036d3b0924bbd088d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e7218dae637efc75e7879467ff9e59", "guid": "bfdfe7dc352907fc980b868725387e98876e0fddec8b7a158df3805c8f53a5ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f4610790278b80c818ba52a4299d7b8", "guid": "bfdfe7dc352907fc980b868725387e98d89bc34f0bf11ba702311e4441528c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f6495307506d5fe9664ee9c31926f5", "guid": "bfdfe7dc352907fc980b868725387e98534f86c80360550061c1615360a19c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0c9a09fe373a00cf247be268077e8e6", "guid": "bfdfe7dc352907fc980b868725387e98ce7f63b1028d7f975e0d8f62eb9cf8da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff837e04b4277c55e3b80eb48bcce42a", "guid": "bfdfe7dc352907fc980b868725387e98c1fffe8709f66c59f794363c6c280db2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a32838f3eb5af98625666493efc95536", "guid": "bfdfe7dc352907fc980b868725387e98b0dada825fc882ea46f84ad7ceeecde9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851f315187b741ddcf177c89399200aaf", "guid": "bfdfe7dc352907fc980b868725387e98bf7ae51413602f31402f6108bd88849a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fea9f28fb116522e2e2acfdcb6780b66", "guid": "bfdfe7dc352907fc980b868725387e98642d09c6f4d09cf2e8fccc08eaa5bdfc"}], "guid": "bfdfe7dc352907fc980b868725387e9818a214aa9a12eddf49b26a81fac412a4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98e80cad606cdb8562f34871f69a635336"}], "guid": "bfdfe7dc352907fc980b868725387e9888ccd7d6318a7aecc69bd1c683731da4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98364b0bc985bc2e5f2077344e04289a09", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98ea730784e531c7b29a4d3807c4f260df", "name": "Protobuf"}, {"guid": "bfdfe7dc352907fc980b868725387e9840fa72b1389229bc82a786c7bb54bc7b", "name": "SwiftProtobuf"}], "guid": "bfdfe7dc352907fc980b868725387e9882f8d386d4480ff95a26a30c940edf12", "name": "reactive_ble_mobile", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ad15dc253cf5cce54073e74a8e492f3f", "name": "reactive_ble_mobile.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}