{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9882b8920d7b231e5735fe782390606cba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981410c4cbba0943e1d366b9dabb27e1db", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981410c4cbba0943e1d366b9dabb27e1db", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9891a9c4656ff6bd931ed3a98869fe65dc", "guid": "bfdfe7dc352907fc980b868725387e98fbc702eaedd4bfca78d105f13a70b613", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d17352641b9324b832dbc62bf98ad1ab", "guid": "bfdfe7dc352907fc980b868725387e983884dc90b8b42e01ec50a0d846553c2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5312487a835999edcc6bed3d7a75dd3", "guid": "bfdfe7dc352907fc980b868725387e98d354b462cae50da99b99aa4402d77635", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980628eb7f73a4ee212c84a2ba288c3d74", "guid": "bfdfe7dc352907fc980b868725387e986302c7a6a06f94fbe5a4cfd108a195f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeef16d2b0b2da0274899403904d7613", "guid": "bfdfe7dc352907fc980b868725387e98e04e2ae0d24cf19846cbe457a19a47dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814dcc8be7dcad0f1622790e46f30095e", "guid": "bfdfe7dc352907fc980b868725387e98fc1cbd9d1acf98d9e6cf0f7ac6a90ec3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0bcad945df51672550919a6454fc7f4", "guid": "bfdfe7dc352907fc980b868725387e9818d3896ae10cd11e3d30dde99a68588c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22d3708097c720bcdc5f8b6a9fe726d", "guid": "bfdfe7dc352907fc980b868725387e98da4e0ccbb9c24cefbc1f917f7256378b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ad1b0a644c0ac82cda9365fa730f90", "guid": "bfdfe7dc352907fc980b868725387e98230602d2b1b510f17056c29f8bb25252", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e92f464976049be3740025d5d7d7e0c", "guid": "bfdfe7dc352907fc980b868725387e98ce95f536f5ac3dd5f897454d0cd7096c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814b31e8ff9e44dff8c0aa551c215b624", "guid": "bfdfe7dc352907fc980b868725387e987b3a26dd6616c5cd3ae6ea10c7bcbc3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98549f9c83af5e5f47e2ee0fd75ab14169", "guid": "bfdfe7dc352907fc980b868725387e98d4c5338a73d9357c218940ca4852b06a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf6ccb39b0e0da0c2137d6c72ff7457e", "guid": "bfdfe7dc352907fc980b868725387e9824f46e12e60cfe325e903e3ddca5bbe9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c88270605744e3ffbd3f2f343fa406b7", "guid": "bfdfe7dc352907fc980b868725387e9829403ac69d0816ade1c63c694147060b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819d371d38dc3736238e2d30abd6356fb", "guid": "bfdfe7dc352907fc980b868725387e98a5bf4ac606b8c39f0bf85024c5937632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bebf041fa9194e6acc3c6df24646a6e6", "guid": "bfdfe7dc352907fc980b868725387e98c36f1e9f2091843073f3d8285326a3eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868ea3c5a131b9edcc64bfe5e27def563", "guid": "bfdfe7dc352907fc980b868725387e98031750c50923a6c5ee5de744c15f9890", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882da22c886bfb312a7fe9c8622d317bd", "guid": "bfdfe7dc352907fc980b868725387e9865cd0fcecbc83e19a31353d6cad90455", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f663df7b6580a3da112f47d831dc74e5", "guid": "bfdfe7dc352907fc980b868725387e98eabcfbd0cbcfde61b447fc4f94c4fee7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c39e9a06ace74d3c71445c18db1fa151", "guid": "bfdfe7dc352907fc980b868725387e986edb580ea54331aad54b18c9a44a64f4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988aa83ae9cd03bd75daacc9ee3716b633", "guid": "bfdfe7dc352907fc980b868725387e989e29793aa12327ca70cbec80827193b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea90dfeeb876e5db7acd36f8cca57b1", "guid": "bfdfe7dc352907fc980b868725387e98f7860c3371f1b12ea8fd4361d731257c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dea565faeeacec87be9a986bba8fe93", "guid": "bfdfe7dc352907fc980b868725387e9850f2ab391d38c72735bd9776bbfa7a4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a05d7aca8a103be550611d59531de4e5", "guid": "bfdfe7dc352907fc980b868725387e98cfe7cf654d1e23f160449e40a7806de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867564e8058db8544c2fe9ce3359b39e1", "guid": "bfdfe7dc352907fc980b868725387e985fff404a7b9b4972d09ab0f20b7c7a2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ab5a61248e91d7a0b1a5eb1ecca97e7", "guid": "bfdfe7dc352907fc980b868725387e98f4686d90b002955dbb0ce315ff53ab65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc177dafa2bae16a73d066dbb43f721", "guid": "bfdfe7dc352907fc980b868725387e9808c176f16d27259d5b48402f2ed27d71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629d646ad31720382904e6c7e5b9ac3e", "guid": "bfdfe7dc352907fc980b868725387e983ac8fff2009595ab2efd438698a525b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cd8dd11a63b7432351fad19765b3ce4", "guid": "bfdfe7dc352907fc980b868725387e9810f77ed7c57412caad24cfc87232a02c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1012c9a8caa6cfb353a35be944f52d7", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b65354ae52e22ae1d997e892b0c92a3", "guid": "bfdfe7dc352907fc980b868725387e980ca5e66c2de3486d99bb194423df915a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877fd76801d9ee9ba28af1ec63bec0304", "guid": "bfdfe7dc352907fc980b868725387e98a51a8f4d5a3211186091e4ec4e9bb2a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dccb7718146b3464b42e3b3011fab714", "guid": "bfdfe7dc352907fc980b868725387e98b5c95a6073f2030a3fa7d1e47f2fbd18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caf9a94c456c16ffbcc50c51e54dd046", "guid": "bfdfe7dc352907fc980b868725387e98f0587ce39ef8faded9c98fcae28c4c61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aabf8d4449d17daa80a6be7a7dd638d", "guid": "bfdfe7dc352907fc980b868725387e98708284fd653c418be4a107595f1464c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d017ab399ff1e1f01f6ee4e1cdf2a273", "guid": "bfdfe7dc352907fc980b868725387e98d4c71d47c35817cca9bec9d6757afc2a"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}