{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b690959e0c4f6182b00c24da5cfa11f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c250d42801dec5902d108e49d8bd01b5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0763413af8ccaa9d98ede86abdc5d44", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a2746edb212f9f19fce4136519e543a3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0763413af8ccaa9d98ede86abdc5d44", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d67aa960badd4a404f2102543af17722", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824d40d0a435154e6fd7d2cf89590d2db", "guid": "bfdfe7dc352907fc980b868725387e98f6fffcbb2c645db700aca2d4a148bdf5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8acb9a784b6f126937b674c23b3a4ef", "guid": "bfdfe7dc352907fc980b868725387e988f6add5fe30963fb4b8827e074c5a2a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4b97ce3862d8e8218d5e9a5078fd77", "guid": "bfdfe7dc352907fc980b868725387e98caa81644101aebc5e49b853aa0736999", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f939c2f4957126ef0e151c5092371e", "guid": "bfdfe7dc352907fc980b868725387e9830852fdc971ed5ec687afa14c852581b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dbdab10d94d29ae2632398ddbd2c5f3", "guid": "bfdfe7dc352907fc980b868725387e98a8e81245d71e1f374ebe28e69d7a975f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f461441ea3b1f377ad322766801775ef", "guid": "bfdfe7dc352907fc980b868725387e98d5372a9c244cd620889b74da7d938337", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a7156549232c59a19855513d3f4f4862", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989795b70951bdd04f1b19a90ed6a962a6", "guid": "bfdfe7dc352907fc980b868725387e98d660db110d104c9385a7e398031656cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0fa4469ea2eb15b9f454ad0fb897b34", "guid": "bfdfe7dc352907fc980b868725387e981e8c899baff5bfe66d224f63218b0c4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d854806e959eda74b3a15b5c4d8f17ed", "guid": "bfdfe7dc352907fc980b868725387e98cf8ebc8576db3aeb48bb7f13fc2babb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880890130c623752d2a03539a31b14776", "guid": "bfdfe7dc352907fc980b868725387e98f2c8e3230ceaeecfa06bcbe90de5e915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893fe7be1bee59ea21cde41c66df1b6db", "guid": "bfdfe7dc352907fc980b868725387e988a80949da225cfc101778202d3cde673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c91c1c5dace11d079323a2d1f6e946", "guid": "bfdfe7dc352907fc980b868725387e98d19f646ff9c25fa972f5b01d04254502"}], "guid": "bfdfe7dc352907fc980b868725387e98faa9db5493e6fdca4f51a7fd3f1ec7ff", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1a8d1c5790182d4835bfc5119136e43", "guid": "bfdfe7dc352907fc980b868725387e98a2fdc5ed7e93b1aaf233dea95d724b39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e982b807bf9d2062df4b0014e0b1eff19bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7340f73f00e5c96b91cb13a0391d8ea", "guid": "bfdfe7dc352907fc980b868725387e9854b8ac82c0a96231a7fef307a5766891"}], "guid": "bfdfe7dc352907fc980b868725387e988aadda36a13d979a63644401f661338f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987fabd08213ac4326d52499cab2ee5a18", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9817d41af66eee3a4c1e145e17163b22b8", "name": "flutter_sound_core", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ed846bc5edbcc85d935ace19b53742e0", "name": "flutter_sound_core.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}