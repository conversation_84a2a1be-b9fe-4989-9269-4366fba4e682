{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843505e38067aaf48521b7d9da9914eed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d73accdad99e982468376d7bfcfb8c3c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e101aa7bf8f26241b71e635c96737324", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984c2e25a3ad09f61b15fc97487d4f52f3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e101aa7bf8f26241b71e635c96737324", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9897194cc5e1f42fb4d4eb4cdadf86a46f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e408d5380963087298bbdd1541b5263d", "guid": "bfdfe7dc352907fc980b868725387e980ce44958e577547a96c69b97af5ce299", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964b9a9349b6f840c2e2d955ed9ddf20", "guid": "bfdfe7dc352907fc980b868725387e9897647097ab45a2e10919652f18e1cfb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca1ae6e27892c767a547cb18992d487", "guid": "bfdfe7dc352907fc980b868725387e98be52101e118e037632c15d20757177e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ae9abd2ae435359a93b2b6a58efddce", "guid": "bfdfe7dc352907fc980b868725387e98f5795f0aec99de3de9cf44e429d896b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983425ff44f64306968fd67e1ae4913f80", "guid": "bfdfe7dc352907fc980b868725387e987e5870861970b9ab8ada1e2c4b340250", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd16946d939e3f8839873ad1d025dede", "guid": "bfdfe7dc352907fc980b868725387e98619d2a91f9022eed68d36eb6b0771df8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee1e25d79bea7142f0484e44c85a3cb", "guid": "bfdfe7dc352907fc980b868725387e986aca15bc2e7e4fb178111e236d85afa7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddd05cb5db5c15844e3feab0c2b07bc3", "guid": "bfdfe7dc352907fc980b868725387e98770f5eadfb4e65f81b1e9d6b6ce7757d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c0c7cb4e6ae0933c13fd82a8098304", "guid": "bfdfe7dc352907fc980b868725387e98042563227e6f8495f53a7b3d66de3050"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fcb30e24ffeb1b97d8f61504fb912cd", "guid": "bfdfe7dc352907fc980b868725387e981d94d1151d1b7a8682cfd68b85ae6d73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db055114466616e84b8006df70c8054f", "guid": "bfdfe7dc352907fc980b868725387e98e77a93decb2742ac5f731c7bc8e2879c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d525cda38fe8ee14cde28f428ce42030", "guid": "bfdfe7dc352907fc980b868725387e987f189f38bc0c62f92e14399422f979b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e1e01947b8da9631914236f82a9ee0", "guid": "bfdfe7dc352907fc980b868725387e9893af45e6aa2f8edf839dbe4882689a77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c9ebe641006b08857009bc9594cb02a", "guid": "bfdfe7dc352907fc980b868725387e98ecffdda04fcb778d4226e7ed0d1b60cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982df9dabd3c1656069fd46b2cfc849c6c", "guid": "bfdfe7dc352907fc980b868725387e98c55a7e01f5fced6fd23c18c989bc2dd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7e2d79f9569db35706bda4c1192f0cf", "guid": "bfdfe7dc352907fc980b868725387e98be4a08d0fa4a057dd1f24ea1b0ccbed3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872139ddf22e8dc3bae97db989eb6ff56", "guid": "bfdfe7dc352907fc980b868725387e98ed9e2f5fa8a9d67050783f031c9b2ce2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98447442989d98ffb77defb78be6af41e8", "guid": "bfdfe7dc352907fc980b868725387e98e496f017ee6571f38481073d809b2f79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6c45daabd3e233e4dce3fdcce0f8ec", "guid": "bfdfe7dc352907fc980b868725387e98a90b14a567df847656a9eea09eb63831", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986841f41804df28c594806a2a8de79e2e", "guid": "bfdfe7dc352907fc980b868725387e98b68566e560c8ce6c4c42e89c438cd46c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce6ed583b279aea7c2f4453466c34c39", "guid": "bfdfe7dc352907fc980b868725387e987edd1e1b3c85e748b97b53bc8ffa010e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980248ea7af379aa71d8252de19dad6e20", "guid": "bfdfe7dc352907fc980b868725387e98586588d4ed7d82d0649ae197ce713a8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8fe6dd5cdf433af63d564309885531e", "guid": "bfdfe7dc352907fc980b868725387e98bb5fbf75f50fe9c4e588c5ae2e9ebe82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c159604a0bfca5a2f1e8267862d132c", "guid": "bfdfe7dc352907fc980b868725387e98de6a268aa0d15e0ddd651b55490dd3de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ed88c968db59b2e2cc7528d8e67a9a", "guid": "bfdfe7dc352907fc980b868725387e98f1482d711d0f4c896e3ecc7ff6cecad2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c085712c3a4fea32ea219c1129c01cf", "guid": "bfdfe7dc352907fc980b868725387e981e0f0041bbece5711a29f35d08ea5389"}], "guid": "bfdfe7dc352907fc980b868725387e986f33f0bfe73a290f6aa65afda315ab9a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a28cc621b3f853d31fa27f6ea62aefe8", "guid": "bfdfe7dc352907fc980b868725387e98ed281c77510f81f1a06f229c90071fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890ed725b8bcdafd6c26f7134a2518916", "guid": "bfdfe7dc352907fc980b868725387e98993906387df71145f4d8c39b8d253a5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f5836c6f486d7618e35c509b1814c62", "guid": "bfdfe7dc352907fc980b868725387e98fce4cdbc96e7634c9a8c978f3cd0a164"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982186516ee4ce5e25cfad687904116ee1", "guid": "bfdfe7dc352907fc980b868725387e98a9700503761f484e3e2e8d4c9b9cd11f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828302f7903748235ddcb2d6d68a29a20", "guid": "bfdfe7dc352907fc980b868725387e98f2511547eb7c4882bffb5bfa7ddf65bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98231216202677714fc3617fe62c815341", "guid": "bfdfe7dc352907fc980b868725387e98a530072de4f2d2382a6299c150ef6a92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b02e6978da4a96e4aed6e64d874317d2", "guid": "bfdfe7dc352907fc980b868725387e984a9c74f6105782d0ac958c76e6c6e9ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d774d0aa89c0a0049d608ad0729a962", "guid": "bfdfe7dc352907fc980b868725387e98654aacbb91b3b458a3c709b1aebeb6cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb95ee7463cf81d6c1b5e288df477944", "guid": "bfdfe7dc352907fc980b868725387e986da135671f778eaf930ac83b4ea07302"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a640784ab4add3d93fab368e44dbeff9", "guid": "bfdfe7dc352907fc980b868725387e98b476c437cf82dd74b6f1017f72c84384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fde6f00206c7ea695d5fcd65f03a76b6", "guid": "bfdfe7dc352907fc980b868725387e9824b8ff3b155949d83ed834cacd359aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98110b495aa3c0518530ff66cf1cd9b614", "guid": "bfdfe7dc352907fc980b868725387e98b7ff612b537c1c3c3dfb79b3dcf205ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af1241e35b8098dc0ed7de761eb966c", "guid": "bfdfe7dc352907fc980b868725387e98fc99789ff69f2d351143df63519f7441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706143f5ffea8d93ef402daa0c7cad4c", "guid": "bfdfe7dc352907fc980b868725387e986327906ee7b0080dd393ff15b5f9234d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e89ace186b6e4ae837a2d6be0bc71b3e", "guid": "bfdfe7dc352907fc980b868725387e98e7eac464dd057144f80b8e07881558e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898673d6c8a776de9d3be83a9c947b631", "guid": "bfdfe7dc352907fc980b868725387e9864ec7bb1f2ee1c287c380caa28598478"}], "guid": "bfdfe7dc352907fc980b868725387e989ed1100f10bd139354034c5c0c95abec", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98bdbfbad39c3aab3b1a4c43d8a60110dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870350cda99093f910f2f9eecd3826fd9", "guid": "bfdfe7dc352907fc980b868725387e98f2a23f0935218425b9e1908e5cf21be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986610c408657e64db4bdab3f032e5a171", "guid": "bfdfe7dc352907fc980b868725387e987edc1326b9970eeef0f78ee61c71ed0e"}], "guid": "bfdfe7dc352907fc980b868725387e98081ce822e00cb2fa13ce16ed7f18c44b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98709b29d4d9c03d1c55a3d6351ee5a6ab", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9847bfd6439d7cb1ca604f9212932b5e20", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}