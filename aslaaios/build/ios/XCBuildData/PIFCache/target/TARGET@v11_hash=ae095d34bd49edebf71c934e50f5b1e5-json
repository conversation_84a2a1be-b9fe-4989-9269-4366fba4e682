{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f6ec3891a0be6525111807007bbcf76", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849c570bf8cc03916a0a29330aeef74d8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988db807c74690161e03dc575ee7464945", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e04009227bba1d35a089c683bdccb547", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98778a51cd43710d278531fd4c4e5ed80f", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985bcd335cf299c92fd14be2a115fef993", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9847c36cf1fe2165ccb62332bdeff26348", "guid": "bfdfe7dc352907fc980b868725387e983202a4c4e9a578f62403eed7d1b8f053", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ac24f61f0b00298d722f6e862c37f385", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d314b0e0b0622c4b6805a741f2686226", "guid": "bfdfe7dc352907fc980b868725387e988784a39cfa7075f5ae0edfc27d0639ee"}], "guid": "bfdfe7dc352907fc980b868725387e9808c42ab3ededc69145f0a612eb33cb6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98aa804376f459d3f0b4ceaf7da246dcc4"}], "guid": "bfdfe7dc352907fc980b868725387e9848f404955103ab434c75240b553e509b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c4d7476648a722a1aca18d3db1c21147", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e9832e568d1d64358cf46b257009674a4a8", "name": "GoogleMLKit"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9896cd7ae8c7639d8f9257b5465384bf6b", "name": "GoogleToolboxForMac"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e986a3314f48204dcd3250955aef3b5b25c", "name": "MLImage"}, {"guid": "bfdfe7dc352907fc980b868725387e98ce8e18cb7dd8a33e75f807c37bedf494", "name": "MLKitBarcodeScanning"}, {"guid": "bfdfe7dc352907fc980b868725387e986aa58d33b8894c02f49ea32c3da727fe", "name": "MLKitCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e9841a9e73b2585cb546680d93608f0e002", "name": "MLKitVision"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e98ea730784e531c7b29a4d3807c4f260df", "name": "Protobuf"}, {"guid": "bfdfe7dc352907fc980b868725387e9840fa72b1389229bc82a786c7bb54bc7b", "name": "SwiftProtobuf"}, {"guid": "bfdfe7dc352907fc980b868725387e98d6a3e96f78013eaa4ca36aa0bb49af35", "name": "audioplayers_darwin"}, {"guid": "bfdfe7dc352907fc980b868725387e98144902882b713248a71c322fd5b2f4ee", "name": "connectivity_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}, {"guid": "bfdfe7dc352907fc980b868725387e98e2ca95742fe9145d6e85576b86908ca0", "name": "firebase_messaging"}, {"guid": "bfdfe7dc352907fc980b868725387e98579f270da12a5d081d8785cf82f3dde0", "name": "flutter_blue_plus_darwin"}, {"guid": "bfdfe7dc352907fc980b868725387e98f49b18868e69e442795477541dae1d9f", "name": "flutter_local_notifications"}, {"guid": "bfdfe7dc352907fc980b868725387e987cb9c58d33d05677fb576674bac57313", "name": "flutter_secure_storage"}, {"guid": "bfdfe7dc352907fc980b868725387e988e2765468126b8189d0a656452a5242d", "name": "flutter_sound"}, {"guid": "bfdfe7dc352907fc980b868725387e9817d41af66eee3a4c1e145e17163b22b8", "name": "flutter_sound_core"}, {"guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple"}, {"guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios"}, {"guid": "bfdfe7dc352907fc980b868725387e981f000f066404b97b12e9c4ca84d38d0f", "name": "image_picker_ios"}, {"guid": "bfdfe7dc352907fc980b868725387e98b54f2bdfc3ce691d3ad04972a364d2a5", "name": "mobile_scanner"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}, {"guid": "bfdfe7dc352907fc980b868725387e9830037b09fee48cfce1f8562d753688c8", "name": "path_provider_foundation"}, {"guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple"}, {"guid": "bfdfe7dc352907fc980b868725387e9882f8d386d4480ff95a26a30c940edf12", "name": "reactive_ble_mobile"}, {"guid": "bfdfe7dc352907fc980b868725387e9828cab1f188854e0a973e6ff6905c5ffe", "name": "shared_preferences_foundation"}, {"guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin"}, {"guid": "bfdfe7dc352907fc980b868725387e98903e66fa03d6d27edaa18126a82c20fd", "name": "url_launcher_ios"}], "guid": "bfdfe7dc352907fc980b868725387e98312b4bc59bbbe2c06c205bf4da6737f5", "name": "Pods-Runner", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98699846e06e93b50cafdb00290784c775", "name": "Pods_Runner.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}