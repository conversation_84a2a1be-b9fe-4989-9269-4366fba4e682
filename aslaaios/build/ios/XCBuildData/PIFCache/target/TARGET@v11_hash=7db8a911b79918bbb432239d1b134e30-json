{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987e91c425d83be4e5e1c4500537ceeb2a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985a04db3c3f34fd629df50f16c960c5c2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831b14aa04aa3a142c876afa223adf451", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5ff70e2ace8ef0184c0993bb7d78abf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831b14aa04aa3a142c876afa223adf451", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dced14aa3b2da7ae33886078a405b77c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ddbff2fef2834f8070a9159f6fea9f92", "guid": "bfdfe7dc352907fc980b868725387e98fd23f8571b3a3abbcf4870de19cf9b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98770a0339b5a17426fb7af9493ca8698d", "guid": "bfdfe7dc352907fc980b868725387e98749ab4ac0aa915eaaaf6b26a6d0488da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0c6ea79c9bca8c4cac5318e9469487", "guid": "bfdfe7dc352907fc980b868725387e98eade7fc341a202d975e3bc15a233a99b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98452ca98c60c1ba812eb98617d7edb953", "guid": "bfdfe7dc352907fc980b868725387e98d13c3dddc06e7a8b156d0fdaf5b05a36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819780539fe3502c422abd24e4fbbc93a", "guid": "bfdfe7dc352907fc980b868725387e98b6ee5b723a6f4affc2d67c986165e62a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c827e374ff0c7662270a34fd75a8c523", "guid": "bfdfe7dc352907fc980b868725387e98c2c4114aab1d80c23ed783ebad12ff0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985223d4e2c0ae0ed18329488325089e25", "guid": "bfdfe7dc352907fc980b868725387e98e513e7cab2850346a0d0a548d366fe23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b91017059480845cea251fa72e4a555", "guid": "bfdfe7dc352907fc980b868725387e9880ed480e41f88244ad1d430384a3d972"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899cd0378fde5fd94a80d33fe7f6b6441", "guid": "bfdfe7dc352907fc980b868725387e98267e5582c8b79b1e3f766048e0f64269", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802b7e340fd1019a350c4dd2a3f2af7f", "guid": "bfdfe7dc352907fc980b868725387e98c4ed20ce83e69058c3120526bb2ec91c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a618d6753db75c9a745a9e297f8242b", "guid": "bfdfe7dc352907fc980b868725387e987672c239c9b084020dfc1132eb382868", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff05d231fe7b3c93737d59298bdabec", "guid": "bfdfe7dc352907fc980b868725387e987fc8df521f28f488020fc719e5230a62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98111aad78c3927b211214a05609ca8c5a", "guid": "bfdfe7dc352907fc980b868725387e98d7b4f1adbbc66235d5b604974f87341d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e54d54079310f31abb07efbe9b8841", "guid": "bfdfe7dc352907fc980b868725387e989ad0c16d9aced7c79c6b886543390f9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7dfe792046a0c2c053922f5e9f5b4c9", "guid": "bfdfe7dc352907fc980b868725387e98f833af7590d598d0014fe73b3344e410"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da48ab4731506da281d899ec7c69f80", "guid": "bfdfe7dc352907fc980b868725387e9860ef68fd90e0b9b29f61d09d4abef744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e0625997110cd12fed3ba921a54cdd", "guid": "bfdfe7dc352907fc980b868725387e9828df14869833c757c9381d919077887a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980225bbe449672c4a2d1bf4c421fd45ba", "guid": "bfdfe7dc352907fc980b868725387e984d368c22964b02fc14d060f269423ce6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868960bf48a81dc0a6b2a391baa945707", "guid": "bfdfe7dc352907fc980b868725387e9868226a485898927016c8688076881e18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98830a09b579e790c0082b355605a3cd00", "guid": "bfdfe7dc352907fc980b868725387e983552670b66b912d01bec4c8da1ba3187"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f024a5fa20206ad1b67b27f3ba6b7695", "guid": "bfdfe7dc352907fc980b868725387e98494ebcbe8ae2af982e4bf6cf1841daf3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cb194aee62e1b9a981be42aea04651e", "guid": "bfdfe7dc352907fc980b868725387e98384d515c1044c45502adbc0597a1b890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810d1da66416368daa53f6fd9644a757f", "guid": "bfdfe7dc352907fc980b868725387e98eb0580f8d4c608e62f4ae9658e56c7ad", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98656678a2152261f68bbb77fef1010d15", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988be8a4a93d48d1e0636c5cd3ea5dd09c", "guid": "bfdfe7dc352907fc980b868725387e98d11be3c92ee2a532c8d5b8eb45b07446"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836da1b95ee19a2388c62618c47abe53c", "guid": "bfdfe7dc352907fc980b868725387e98b66f54a32620b3780a4cd809f75949bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2125ece11c8f5b0dc53451a5ae454bf", "guid": "bfdfe7dc352907fc980b868725387e98221ece228d4497a1e8dd42021e2d8e64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd44bb2bd47ef1b763c7f77f978284d4", "guid": "bfdfe7dc352907fc980b868725387e9859ebff484973f609da25cf9d5754ad17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98457b38747647fa2d3d99e0757a4e9b0a", "guid": "bfdfe7dc352907fc980b868725387e98f562edbc4668fb909ed64a3453964442"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876735c6247cb2f4f49d8a167fec14d22", "guid": "bfdfe7dc352907fc980b868725387e98a828f42cce33102f42ada2bc9093bd7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d5e81394e8d4918e92c243946d3e874", "guid": "bfdfe7dc352907fc980b868725387e9811418d5dadf63e6aa8a31d2f6b51014d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982106fe8be9807fec098bab8c1501908d", "guid": "bfdfe7dc352907fc980b868725387e981cd47b47ec392b67e2a0830dd7d58939"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac267ed5ba8d823be38c7d574e56bc5", "guid": "bfdfe7dc352907fc980b868725387e9817e6c2c4103f79fea494bc98091bb8c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844ec6969c04df020077aaeac3f0b9667", "guid": "bfdfe7dc352907fc980b868725387e98b555834f2c6c4787502e0228d4b093de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981be08df0e0e21099f187a46c29d5abca", "guid": "bfdfe7dc352907fc980b868725387e983956849e987ee2745bc3edac8dcb0d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831757b62d562dd4ad2d2f9f705216a43", "guid": "bfdfe7dc352907fc980b868725387e9801bef832f1cdcb0840534574fa8df735"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3c2cf64cdf587dda98459b2b98b683a", "guid": "bfdfe7dc352907fc980b868725387e987f546961b6a12f9d0f550ab9a082e8b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ad62583810258cca9ce7d518a9ffac", "guid": "bfdfe7dc352907fc980b868725387e98256194e4fb2def249be60f4b38fd7969"}], "guid": "bfdfe7dc352907fc980b868725387e989c656b5e910c6c3296398b9328bf55e1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98afda652134dcba2a91884e2fdca6c265"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7dd3308e62ff3506ddc7ba6752920a", "guid": "bfdfe7dc352907fc980b868725387e989011b8bf59b7fbbf96085cfd40b067d1"}], "guid": "bfdfe7dc352907fc980b868725387e988e1ac8fd3c72b2b486c7443c998bd0a0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ecb92084595a12ab33a6ae3d29661d63", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9801aa79610b4852abdbca77baebd1a463", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}