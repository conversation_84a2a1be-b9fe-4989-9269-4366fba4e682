{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98e1aec94ac99ae867c3e5f2d549714e7f", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98f82069cc4ce8bc877b305cd4c3c37c84", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e981ea78178c56c0aa2fb428b3e48c1a991", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9893c8d25acc385c173651f6d115d2a123", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/darwin/audioplayers_darwin/Sources/audioplayers_darwin/AudioContext.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e922d65292062d47152b524086a43f78", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/darwin/audioplayers_darwin/Sources/audioplayers_darwin/AudioPlayerError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98268d144b818195a36a425a0031b54906", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/darwin/audioplayers_darwin/Sources/audioplayers_darwin/AudioplayersDarwinPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987256f26d62ce2cbebf2883677f45183b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/darwin/audioplayers_darwin/Sources/audioplayers_darwin/Utils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2533ec732f9eee72c2525319d535ac2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/darwin/audioplayers_darwin/Sources/audioplayers_darwin/WrappedMediaPlayer.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ff8014a849d2436828736b716b42e46a", "name": "audioplayers_darwin", "path": "audioplayers_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828f746ce54905dc006fa014333b19bca", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5291223770a923fa20dc1858ff11146", "name": "audioplayers_darwin", "path": "audioplayers_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f4d4e2819084fa09760e9d32d55e792", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985286db8fdc953c4345bf2be1e5b1ba32", "name": "audioplayers_darwin", "path": "audioplayers_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d95711bdc51285b65f52c05b91cc857e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811f34e7cc3171f6cd9199349f3759a73", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d13ab443789c74dab339676975787d2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce0010631cedf3f5e2cdc837776694e8", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986aed0fe80728259ca31bd1e40a653555", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d167c957364f8d8fff508d92e41fee4a", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810eb515c801b758a5296eb8863a4b561", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e64b2e4e00280f2638e01c37d06eae3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b8a2dbd03804bc63ad84a9daf9bb48d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2dc114d364a7fbfb3af030519dd032a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8423acaa196012489372bc944b556d0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986973a4f7701a1a1e9180c8c437966971", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d4a6e12c1428471d27a639a700e97ab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868130283997a86f3503d328b63987216", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b57850d0a2cb3549914d6518d7b49d5f", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/darwin/audioplayers_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981a7db3febf6653d98728340a85664649", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/darwin/audioplayers_darwin.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981b9b28a79de2a91f67691f3b25e039d9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989c229ff9ee80355c970689b1662a30fe", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984b83d5be9222037f39876e307de2f88f", "path": "audioplayers_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ea3d92bad7dda9aa8bf5082ff2af242", "path": "audioplayers_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e1a13b216d930c0e93b1723833a3b378", "path": "audioplayers_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f71585026580421e235b5ed1777beeec", "path": "audioplayers_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a60a735ecf56e5268dcd23f8e5ffc90a", "path": "audioplayers_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98daca499cca2b701dfc3277a5a8f3ac2a", "path": "audioplayers_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e781a7c9d7bf5d483005e85303a078b5", "path": "audioplayers_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98278791a062773800f4cfabe54cce46c4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audioplayers_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985438a06f2ea796591bfcf17b0176374f", "name": "audioplayers_darwin", "path": "../.symlinks/plugins/audioplayers_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989270a1094bb0f5be469a6c712a5b8adc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b3634df61275d185de3e0072319b5784", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0fcd0f7552edcb5d2af1dfba305be63", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ba03077a9b290d642a5dd86e9a0d26f1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc51df03a75e042fa19cdcfd3691f9", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb5647e5a5a3b903a51cf95c82a0566a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c615ea452ba003260f2c9d5226bc9f8b", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857b414fcce095462b7a3a131b8fe1b20", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8a8e22ecc8faaf251fe707cf88c5ea7", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd0df505831e7f099d4193bddd3befb6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7dc72317719ad0b180b9fa28fa5cc9d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851585e92929e349902f9ca66c12c7785", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981bd23dbba774996a790cf67fc37df912", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2309fceb1999f8ad96fdf0f71686925", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980be508bb641cc9d9051eee7e9d1a718e", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988674ebe597041425740ccaf64a8047d8", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98052371d6fc54502aad57739833174be2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8cee1fc58e1453b847f4125e7a9ab28", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7466791c4fd7c8001b513dab0efa06c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d70503eff01d83b75b3cf8a848547b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4df52619dcc795cb5ec449a6251dae7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886144e30e44aac602c41dacbfe4959f5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98176a803ca14b768fbbc8768fa616d368", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858f57d995a5fa47bd9318fac3660536f", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e4d114a073b3521a08a5b266cb32f3ab", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e984050a69c492aed4b6c2b018a74843feb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a64d18995da26cd9a8d2de637e2f1943", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9835b3db19b3ab63466bc4312ed2f2138c", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f532717746a5651b6da0d0dedd7f94a", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cb363a699bd2dea844ae74897af017c1", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b62962aecebb340a80023e1ac24be5b", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3a158d0e8adb6b08e538fde10faec22", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980e85dd8acde98e5b40de0f835e3c7562", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a13b114977f20e74adfb60d60cb5676c", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d74c53f12da4e89651f87edc3e73982e", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98349051703d95d142d758470b8d876dde", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811f35ad65096e672427ecb5d4fea00db", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980373f76b601030d7811b59fb4078d2ad", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3d3d9055b0ece41526988f6ff5fd1d1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/FLTFirebaseCorePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824aeda67fadad3d0b30c0edb32716c50", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/FLTFirebasePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9803ee5e7b6ccab88e5bf8ccf656489d3e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/FLTFirebasePluginRegistry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98852632a656242ca5c451dd8c6643fa81", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888438ad0ad1bc8f7aa5e415342b7a537", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/dummy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859c12d6e57c438c1da6821fe05cfd576", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebaseCorePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ab368222281af774782da7bddf0036e", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1d7db42132fb1806eeaba73f40b8920", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePluginRegistry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98564a486a6694a3cbf74be70ad273c0a4", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98192526e35f037048fbeff5c6b31dc4ff", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981498020d93142d55b92991a38fd33402", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833ef71d0c6483f6d9dd1d995ce401020", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5d34066e3d618f91aa661bf4c7400e4", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989844d62a1c5d3a8b51896a40f9d4ebb8", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f47d817904080815c2d27361f580d804", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7dd58db3c2c694ad454ca01b17c75b3", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4f544f312ab827e0be72e72644d6fd7", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677071f9007614e6d099aff2573663c5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b506641d0b61596583b94a0693623fa", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866916c72dd37953e5b01de0fa8be7a1c", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b621981dc20129188c0d38a6f123305", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c72c232884187a20b9d1b0d4a0697bbd", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7884ff3bfff316b04663e9559fc267d", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c939d37d4c58e47e1b9cf108fb1d19d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad66afa38ce3642b6e070cdd18aa2673", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8bfba19cdf02faf1890b499eb309c8d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb9fca19e4a14ce5da9524bdec41c938", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca7eaca2d90e00ef71459105b4da4133", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ad9840d7c36406944f7aae86120f3c4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f514a7361c61281a81f0fe743037a6a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894464b96af4f96225984d1e24b593c1e", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983e7bb5bf126518f07bbb4e09e7fe1935", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9898c0d74f82fcf11cf85a9016cfff387f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980952c68833425dd5b4fa86cc571f0782", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983286b25fd9fea5460d936d3036969a5c", "path": "firebase_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854c9d91f210136cbddfbfbfd4b43d5c4", "path": "firebase_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9837eca54ac32d1be6b1cf7b1696925de5", "path": "firebase_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a46fb3ba87f24fb6f3955ed9d215a185", "path": "firebase_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c30096531049201094e7b194b22ee6aa", "path": "firebase_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9813522c85b3045a49e7d8551106b30034", "path": "firebase_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e9be51222e7516373c92a49b2457cfba", "path": "firebase_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9801610fb63296d4cda58458443b963799", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb790cc33768b1a7f0a0515497c9ce07", "name": "firebase_core", "path": "../.symlinks/plugins/firebase_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ffa84b22f4b3dc3e752966e18699628", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/ios/firebase_messaging/Sources/firebase_messaging/FLTFirebaseMessagingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d95955ec529997a434af6be8f04593e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/ios/firebase_messaging/Sources/firebase_messaging/include/FLTFirebaseMessagingPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98099cf173ca7613ec3d9bce939a957c43", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb94ce8805387d9c9c2d40ea47a54e56", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835b0c7d88623042f46cdb4ceac71f8d4", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aee8b2d6b36d152b58f0a3d83638423d", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984894f63af732608d2749a1698cd80659", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98162cd58534eecdbfe45f085ecee7e9b7", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867f0d469e9f28140bc7f2a6e30f6b05d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806a7c18dd15f4f0dc0eaaccc09ea4b23", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808ec3a72d6386b988cfd702876f81c57", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc1fbb4c6128b0eb5acc351e1f8fc972", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988508127e68c240aa95b3958a3341cb77", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868146118dea94a7ecbf2d89add493ef0", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbd1bda6da420882ab5b15d1cde1d1cb", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98629a69a5b8549be74f7230a11d7a5726", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc220aeca654bad29a3add6b315c1507", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1fd31de25f234a901017406ab699995", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3fbc0bcb9b7e664fc8569f3e3d67d60", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867a07864ea5ddf9db72d1dbde37e7eb3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807785beb1b4f45611ae7d8bcb8048a3e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbe4baac81081b0307ce52f3bf7546d6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e99a2060512cccedee3abcf24dadebf1", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/ios/firebase_messaging/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98776a23621c8fd61ab86056ef59972861", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/ios/firebase_messaging.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9835d4d5bc5eca8d78e14de1a3338a193d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bbab59f92a1d82e33e987c289c6234e2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9801d77f84db2426ba65df7709d53bbdc0", "path": "firebase_messaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3bd29f75c02448e2b3d7664b86ba79d", "path": "firebase_messaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f9851fb163759c2c27f312b0a8ecfb6a", "path": "firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984999803e20673e169e5259000dcfaa2a", "path": "firebase_messaging-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865fec6f18a010eff159700185c4e74bd", "path": "firebase_messaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98243c6dfd11ed2333694cb64a2ce51e66", "path": "firebase_messaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986070d74f300a3bb8e25e0fa2be42631e", "path": "firebase_messaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9885eec0d5281645791d2cb0784c8971ae", "path": "ResourceBundle-firebase_messaging_Privacy-firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9829bf85f7181e613e144e06614a019850", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bccb21774f3152f851c5aa866c71f38b", "name": "firebase_messaging", "path": "../.symlinks/plugins/firebase_messaging/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9831797421a68d53d833e988064f7b2ec1", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985a43aafa18c7cad6b003ceeece20bf46", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98293b6cc872a93a25fea221a92cb2cdfe", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bf42214781678274b785d4ce8fc375ff", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9855b1c0f20da86f8720126934608bd7ac", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc6a74312c003bd48302e2410ad15189", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9833ca2e51e6cb038646a65c8f3254aa35", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/darwin/flutter_blue_plus_darwin/Sources/flutter_blue_plus_darwin/FlutterBluePlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e67abe2ca0f496db1608451eab3a07a6", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/darwin/flutter_blue_plus_darwin/Sources/flutter_blue_plus_darwin/include/flutter_blue_plus_darwin/FlutterBluePlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e57183abfddab94903b59d1a8d5ab0f3", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a4761a2530cf836787b9c9034f11d29", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98827879bb1b1a37c6c9d8bc79a565d0ce", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b50ffe1223532af2c6d3490647f6f4a8", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bd81628d446c8dfd9d81853d2f91a6d", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea04e066ac298cb7787e3c3c82f7cf16", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb31081421f7f523fedf6d45e5287335", "name": "flutter_blue_plus_darwin", "path": "flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869667a09ccf32bfb9c041344e455e30d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a12754912b45d65d5007af36d429c2c7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a97d59403d52e78221d04cd8b371a39", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c39f7f50e1318698251197a9a6870ef", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fd380fe6003d50dbd6d3817ad475a9a", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eef224e7775e5e2dde350eeaf7d0d307", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bd9b3c67e24a2eedd9b722a6361b203", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d249cf3725934c1a71157feb9c906b1e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c4f2c3ca997d6408dd7f837514f334d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e263809f8e1f43c982a5bea5c363cdf2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b35abc5efeac7ee7d548bae1bf463a4b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98342dc301802465ae1769aa0825c8022f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989635a1c685ea4dfcd494a7ad04857694", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b31ad631b7b26e3525379dd17123dd43", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98282d6008314be9b4bf90ce7a8f98a895", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/darwin/flutter_blue_plus_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980e8851886df8a3eb29728d52aefa66ba", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/darwin/flutter_blue_plus_darwin.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98220d4b23cb84cebba978758ca775891f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98598c5ac9cd3242464e3faf86b9bc3e90", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f418914ab90d7f71e16626fd3e5cd2f6", "path": "flutter_blue_plus_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98efaa9ad1f7064c3c805b9949867d64f2", "path": "flutter_blue_plus_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b2b3117d616c14b15b7c395961af2f23", "path": "flutter_blue_plus_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a4e9ab262ea8397213a7f382777776e", "path": "flutter_blue_plus_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986facd076ec1c6a322a51a351773096c5", "path": "flutter_blue_plus_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988d06d62fa6bc2c200076de67ae07b1d2", "path": "flutter_blue_plus_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e6548c8908935c09fe5c8d582217cba6", "path": "flutter_blue_plus_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984067b8fe56e761a8a51eb6dc8767847c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_blue_plus_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9a1752c382e65b22c15e25866e9a4a7", "name": "flutter_blue_plus_darwin", "path": "../.symlinks/plugins/flutter_blue_plus_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98871d0c49112a1d3aba7b59b3bc110cd0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Classes/ActionEventSink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf455d0efe707cc5577f7038cd8947df", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Classes/ActionEventSink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987886b40e83b5a3e31735debc533a06a9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Classes/Converters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9833d9eb5df68330de704945897598d5a9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Classes/Converters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987887811bd55420fc8d586715a1db2064", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Classes/FlutterEngineManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dde805d3bbb8129a7680e9a4418b282f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Classes/FlutterEngineManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987531cd7f5332d90a3274c97c2d2efc2b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Classes/FlutterLocalNotificationsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98534ff5e15f466da0cb7b68b2089a8cbc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Classes/FlutterLocalNotificationsPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f79966ec69a1c667b9820c0f667b6984", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e980640310c9fd90401e1ee1c75dab5d4b5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ed41f125ec1dfc8c06ec6ff8f0a10814", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa81dea5dd4aea8d06a276c12e339771", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982062c83a5f110865741413789c845e33", "name": "flutter_local_notifications", "path": "flutter_local_notifications", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984080649f7c7cba3cf1e6ad197cbae633", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986513ae3be8291eb39704e1da63dd389a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989253c94c7f42922a93b5d273f1ce3187", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa2563c61ee74db42d44c06386a4d3e0", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb73d39538a9ad7c5e30d3810e31a269", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ac21d8db47a62270482e8d6dbc5a3cf", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e13f9172c0079628939343fe8f12b5e3", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e2b006dae2fa1ecfe286594bee3a6d6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f37f6cee82ebe8369e6adb93e36fa08", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981825977b23cfd3c70d0a68d504111763", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98145f5dcee6d7d3481ac4bf0b1ac1bbf3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5269b6ed51995d092b1e3fb1aa16ad2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2b3c821438fa18d32c743be801a6196", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983a70dcee1bcebe24c0e18e6b511defbb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/ios/flutter_local_notifications.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ce978ba3f9f5a5c42d303e8d9b6cd4a8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f4429e7a6389dc2f75bf3c39fb1e777e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ef185bc56258a88d14dd870549c5cb0a", "path": "flutter_local_notifications.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b4ca249c1541c7beace1bbf1edfb4d0f", "path": "flutter_local_notifications-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9832d873e1c7f01fb15dcb76e7dcbac598", "path": "flutter_local_notifications-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e53abf3664d9e6f924a183aef701bfb8", "path": "flutter_local_notifications-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d655de4c69a54992180354de893a2c35", "path": "flutter_local_notifications-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9829d47172679c249ba094c18153733f2c", "path": "flutter_local_notifications.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d5bcc2b558539350ca12f2551d6ba943", "path": "flutter_local_notifications.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e4c24e13a04debb1bab84b6b56d70d7b", "path": "ResourceBundle-flutter_local_notifications_privacy-flutter_local_notifications-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9849629768616c16f504feef840d643ce4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_local_notifications", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f360a469b5179835ca5467f533442e5", "name": "flutter_local_notifications", "path": "../.symlinks/plugins/flutter_local_notifications/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98342a1ba831e272501394202a9ede9fa3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/ios/Classes/FlutterSecureStoragePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98444dcb805a42524249cf9d998c8c6d34", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/ios/Classes/FlutterSecureStoragePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983ec98d63fb8d5675fa029e45122739c9", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b074ebd117a51ccc874751de462dbdd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982994d6335e865ca8da4daf6a93089950", "name": "flutter_secure_storage", "path": "flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823c86a29297cd48acea3bfe6fd2250b7", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98961d5392e37cac1f7399283c0a05e17e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab82e51ed7112decb0e6ff7b4fa1292c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871ecddf65d3e4c10090dea42d82bfd9d", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ea0cd2ae2ace8faf5b7da5cce194190", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824f7c3c9b821407643748ff16bf6ed49", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891900535577ed5d5e6f1a6b7e3b227fd", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981287acf36f1ee5d0d700e38422416b09", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce78a464405f7b58be48f8ebf610d927", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd369ae5600bdcda7c971fc7c1c05958", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e358316195c31383a038d904718d94e4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcf68497b8d348193da9594a29e96cde", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989811369a874895f58d3cb69bfe70ff54", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9837b6ef449928c643fed205e5151f06f3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/ios/flutter_secure_storage.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ae1ed6614885baa742d40d0811b7e12a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ed461619a649971016a41571428783f6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9849268a6bee74a8cb37e0edbfd9b1ad2a", "path": "flutter_secure_storage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac50d25973bb97aedd5a709aceb0defa", "path": "flutter_secure_storage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9871a34cdcc73dcf1a49fcbdc766aaf9e4", "path": "flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988aac25c56c6cfda8cf7cf2317781a92f", "path": "flutter_secure_storage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f37cb90d1568a7478758208b3a5a57df", "path": "flutter_secure_storage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e1afc556af459986a7bd500a2b15003a", "path": "flutter_secure_storage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9898aaa641c74df81504d55d1868e9036e", "path": "flutter_secure_storage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982760d6ab0555eb2f75953ecbd6abdb5a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888b3ee028040d3af6d3b8c3849f14929", "name": "flutter_secure_storage", "path": "../.symlinks/plugins/flutter_secure_storage/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b8054aca87510358bcf06d17140b97b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSound.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e98266e518f0477e8d67a7d943f86a3e187", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSound.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9f8b70c1e471703ec0f88a122712283", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985fbe12d246f563929cfe08836a588e2c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e9bd584e87f27c1c615cab60ca6d821", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e989feb7e536c4abc20fcb6fd8cc777f4c4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundPlayer.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e59d896bb36642303c271c06501dc95a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundPlayerManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e985c345d794b3a894e9233f2e978c716c0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundPlayerManager.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98032f7d85fc041a50a947aebb75bfd1c1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundRecorder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e98d5e4ad8718596f5c29a91c8b7bd89359", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundRecorder.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864057d05753394bb571074e9c5d368dd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundRecorderManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e983cea76207fdd6ad4192035e9fb3ca747", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/Classes/FlutterSoundRecorderManager.mm", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c85edb29e5742ecef4118d38ccb0b94c", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f545d6d3da79ed4984435536b0143bb5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b4f5a8013b658335015eaf85a023de3", "name": "flutter_sound", "path": "flutter_sound", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e333ffd7b1ca3800caffa4586f1fbe26", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c6f991adcf1bf698d63bb69099aac8f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984faf47075c1c1b0a05ca4580dab3bc70", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a03763b1c0cfdeda470b04f75c4e460", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d8a50ae1d7c8b61a3acdad81cf68859", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889b20986b59fa882fcbceaa7ac786eb0", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa89ae52bfb1d4c1f4bb09ae9051a0d7", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845f648b1c01b955f3fc68d4e6a02ddaa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818eb240ea353e9e918bbaa608764c724", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa470b66af965ec5d123f6007e7757ce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bed3231c5ea09dc7cf3e1671cedcd3fb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b3973b3c0a1e1e4276c5a0980bec2b8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d4b6f24c8934fb7f7d5db5edfee3cf8", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9842c70252c74a4c0fd6850accfbb4e104", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/ios/flutter_sound.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98315257cc0ffeb815a76cef219f585997", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9800366375b5de76c82a0b1480566525be", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9836d91720e1d18b2921709264c99faa44", "path": "flutter_sound.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b83a13bf503f1b943e8b741519e799b1", "path": "flutter_sound-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981d7660a0805963fe12dce6a3a10b79e5", "path": "flutter_sound-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988034fc65259c8f88dc5b707d2338d200", "path": "flutter_sound-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f412720450e48eba510204747b426279", "path": "flutter_sound-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98296f23ee20c0802519a657f6a7990e47", "path": "flutter_sound.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9815960c9e11599ec483846fdc2f57ab67", "path": "flutter_sound.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980afe8589e082b54a6611371a2c9f2d60", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_sound", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9536e25b952f21b4aead15179e8a873", "name": "flutter_sound", "path": "../.symlinks/plugins/flutter_sound/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98859902834b96ac542cddfc1e6876b022", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/GeolocatorPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98fa3ecec0862f8ee6d0a6bf3575555021", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987967c66444f7289708a8eadcddc15387", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Constants/ErrorCodes.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9874cdf145becab48002c6fde1e367d2bb", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4010cdc3600fbf5f0b8d2c90e4ff91b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/GeolocationHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ef3478f8f9520009ac4956cb4252ef6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/LocationAccuracyHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881b12f864169bbf324785b6a56291750", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/LocationServiceStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d503ff075ebd012961f2d148b411dcdc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/PermissionHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f0cd089ab107db5f44352a6800b46b23", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Handlers/PositionStreamHandler.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d58c2a2e1debf12feeaceb66485408f", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ccd89098739aaa6d4e9fb5d52320694f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c30f76ff5ded289d45871c38d13361c", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/GeolocatorPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859a00c607bed99df7ea29b05f77f9066", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/GeolocatorPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98307fa34b98ed49e02ef15116dcf6cfa0", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Constants/ErrorCodes.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f7019d165706c2717c4d3461b7e12466", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983357a8c6dc18a42a2c4527dd54342bbc", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/GeolocationHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983576b76b5a935472aba8ed9f94a0f762", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/GeolocationHandler_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2678acf359d179cd4353295458e972f", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/LocationAccuracyHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4f8e44f6db36d056fe48de072ad2c39", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/LocationServiceStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875c0b1c6b8f60973a791209f8c9f17e7", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/PermissionHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f08879be0ab80e9fced49fd897b02742", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Handlers/PositionStreamHandler.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988aee293020b80f6154beab93bd7b8681", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d83223130e30085ed889e54faa218a44", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/ActivityTypeMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98122b04258e753c49a3d425053fea1cae", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/AuthorizationStatusMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbd748136a33625d57b6099cccebc97b", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationAccuracyMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d8f63ebd020103ac3649db004a4ac03", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationDistanceMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855f2389606538b815aab54eadb601b36", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/LocationMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b22eade7c29f9bf763e0acc571917859", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/PermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802c48c51a838932b8052706656a1645f", "path": "../../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/geolocator_apple/Utils/ServiceStatus.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ffe48e7cfeca3e4716c01d8d36df6f00", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c230ccaed779a7693c30a7421c47f2f", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877a8a64923b6fe81d80cb616b68c2a44", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985c6c574f52e5b9a0f0fd974d43bb19fc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/ActivityTypeMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98794f7a265d6214e44a14bcefd9f1dde6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/AuthorizationStatusMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ac93b395cea5df7f67d350c77f469a5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationAccuracyMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f5196ed9106eb80fa766eb99548e8c5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationDistanceMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984978cb05fcdf58acc7ee0cb0145df4b0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/LocationMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984e9c2974d64625d38db6bb00f0850ab8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/Utils/PermissionUtils.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989b8bf5470300a4020394d1e7d103e57d", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98243d72cda3372e73553cd7c64bf3fdbe", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef94e3454af79ed2bc1db69f41050ef9", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891b78bce2ae6ea28980b2ddac704dfbd", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984078407541ae383863d75e9b32bef0c6", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c8bd7fd4d6f78403022e5e6e484d724", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98138e2e85004251d8e1b83095e86bb5fd", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98783e8b2d39b0bc37340f4c4f9d39a457", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98126210947e09c3a6d46ae4f9113e11ec", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b9e63451bd797d78c09e607a1bfa79e", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e9061e46ecc907a6e9c544effe972f5", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1b75b38a37cf11d466ac86056812746", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6dfaa4c146bf9d1aafab69c4f5a9cae", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897a4629076fe8e211bf0a4a0185317c7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce33b3bf45e70eb745f3a0c1406470e1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e029fcc4fadd593a282f13c258784d72", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840d3dadcb07966289d7e73bcac23c3de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e4738876485e9103baef8baa3df7fcd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984954b9f3e3352db545446afe1b0f6356", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d3ddccafaadd9d275e6f46bf3fa75c0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810b363c0ef75ddfef1e0ab08e1ccb7ce", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984c0862f3c2d83f58eb347818f394800f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a84ef13c81d074e899d859281b74e073", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/darwin/geolocator_apple/Sources/geolocator_apple/include/GeolocatorPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983ec9c698a03c0bb0d94942a1d10a1388", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a86781a5acb6d64d7129cba89b2180b3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ffb978d02dca70763004362f54ca7147", "path": "geolocator_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9828f33ca2c96141bb8a3a69a2fa7f3550", "path": "geolocator_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98afe8f5792a1cb708eca990aa69d96740", "path": "geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884f59a1d0f31e76ad9eb6ed7b27168f4", "path": "geolocator_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986de28c4f85ffec74c78801fb89bc7d30", "path": "geolocator_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9875818126485ca400f0d34afbc0fc4a33", "path": "geolocator_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98832b9cdaec99a92a9d37a833521f92b0", "path": "ResourceBundle-geolocator_apple_privacy-geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9826c6b823cb07bc3160d3f5ee56f488b6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988fd6253958aa2753f8d79c00a55b0e6c", "name": "geolocator_apple", "path": "../.symlinks/plugins/geolocator_apple/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9891a9c4656ff6bd931ed3a98869fe65dc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMCATransactionWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988aa83ae9cd03bd75daacc9ee3716b633", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMCATransactionWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d17352641b9324b832dbc62bf98ad1ab", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMClusterManagersController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984ea90dfeeb876e5db7acd36f8cca57b1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMClusterManagersController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5312487a835999edcc6bed3d7a75dd3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMGroundOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987dea565faeeacec87be9a986bba8fe93", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMGroundOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980628eb7f73a4ee212c84a2ba288c3d74", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMGroundOverlayController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eeef16d2b0b2da0274899403904d7613", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMImageUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a05d7aca8a103be550611d59531de4e5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMImageUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814dcc8be7dcad0f1622790e46f30095e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMMarkerUserData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9867564e8058db8544c2fe9ce3359b39e1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FGMMarkerUserData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0bcad945df51672550919a6454fc7f4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapHeatmapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ab5a61248e91d7a0b1a5eb1ecca97e7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapHeatmapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a22d3708097c720bcdc5f8b6a9fe726d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapJSONConversions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986bc177dafa2bae16a73d066dbb43f721", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapJSONConversions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828ad1b0a644c0ac82cda9365fa730f90", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98629d646ad31720382904e6c7e5b9ac3e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e92f464976049be3740025d5d7d7e0c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapTileOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985cd8dd11a63b7432351fad19765b3ce4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/FLTGoogleMapTileOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814b31e8ff9e44dff8c0aa551c215b624", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/google_maps_flutter_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98549f9c83af5e5f47e2ee0fd75ab14169", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapCircleController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b65354ae52e22ae1d997e892b0c92a3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapCircleController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf6ccb39b0e0da0c2137d6c72ff7457e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877fd76801d9ee9ba28af1ec63bec0304", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c88270605744e3ffbd3f2f343fa406b7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819d371d38dc3736238e2d30abd6356fb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapMarkerController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dccb7718146b3464b42e3b3011fab714", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapMarkerController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bebf041fa9194e6acc3c6df24646a6e6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapMarkerController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868ea3c5a131b9edcc64bfe5e27def563", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolygonController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98caf9a94c456c16ffbcc50c51e54dd046", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolygonController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882da22c886bfb312a7fe9c8622d317bd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolylineController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988aabf8d4449d17daa80a6be7a7dd638d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolylineController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f663df7b6580a3da112f47d831dc74e5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/GoogleMapPolylineController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c39e9a06ace74d3c71445c18db1fa151", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d017ab399ff1e1f01f6ee4e1cdf2a273", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98212782eb6b3aff2717fa341062387094", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9829169cfbbe639d805ddad8ce51d34b1d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ee4eb0e5a21bbe02fac74302d01d1015", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca0b2450cbcdba22ca338ea15bc1ee38", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b021b13cea02211246d29a73a60fa257", "name": "google_maps_flutter_ios", "path": "google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5a26182dbdd2ae1a314dd91ebf5e6db", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814396cd9932bd204e99bc1de402a171e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c30d3bd82517c71826d2b67a081aee0b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecd051201a065af663c60d458137c0db", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981afec08843594df6cfe2b61234db14fb", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846fa4a046a1e54412740aac51a01d342", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7c63d0aef063c3e150fd05483d4a4c6", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984df28cd2deb8517819ab590a9c52b68e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6b3d81d3e0264949b787cc07852bcaa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec897f00a3cb531eadbe0e820f5ed618", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ced0c89cdbb3933a1185661a4d6d2446", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a80107f6bac233f5b6dd8c382a654416", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d856b78b15262bc9174684e33213c62a", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9888793e25e5c32ac9495280fbcb3fb4bf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/Classes/google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b098449027551aef89b1b7986ee1b4d2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/ios/google_maps_flutter_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c24c6f9ad2b8a5a8464d1f5e73d40f7b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98436f770df861d139f39912e70a090651", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9847d757574fc9c28104074a93d2ab8088", "path": "google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f1012c9a8caa6cfb353a35be944f52d7", "path": "google_maps_flutter_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ff96e1804f52602506d25acccc4225a0", "path": "google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988cd51f5ef67ac26ae4fa9309f63441c0", "path": "google_maps_flutter_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9882b8920d7b231e5735fe782390606cba", "path": "google_maps_flutter_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981410c4cbba0943e1d366b9dabb27e1db", "path": "google_maps_flutter_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c27402d03794dc6460bc82b276ad048b", "path": "ResourceBundle-google_maps_flutter_ios_privacy-google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980f89acc854091b443f8bc0bb55723fa8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985599018be8e066bef5c574551570a35b", "name": "google_maps_flutter_ios", "path": "../.symlinks/plugins/google_maps_flutter_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c3edc2120cde12625fec7ecb6f5de703", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98abb67ffdee6da62af4290353bd90079c", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811f83a9ab061fa361bad31c419a4aea5", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7626ab38990c08952827c39b93da575", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875f9f3bd153e63ffe3cb9e85960bdee7", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c62239e341839c22c275f28840f9c8b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a24fd20164f63211628005c59cf6fcea", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898c0b6e135ed6f8b6c1bdc39ccf56ab8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e263b7ffb5474595093bd1a19a80e721", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988463bbe66fbd6b5051441cd98d86e388", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4b7b9284dc17169e4027f701dbafdb8", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bfb3ccbad7235b794c454def6e63fa2", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98377daf5a0fadda210f978e59c536b6d1", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836bb9242d3d172f5ac4eec9c93fcfd35", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f91c975000d9b334f490a7fcfb15a48", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b904841fe1d6a181f88547f4917f14fa", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc405b56a1c268b33dc758f113ee79a4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0dea9a8414deb80d1e171d967ac7e25", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d172157e9314c878d4ad0afdfa5f459f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983bdc92cbb1df9ee8cf3c8b73fa8d8132", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e60d88de1dbf09225a2ad2ac4e831f8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981157e5d64b3e1942a04e36cb836501b8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98508b34554122c22cd7ac136fbb325cdf", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b7b6fcb24ba7671cdfa28032335b27c", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c108b760705be5dbf5c9218173712694", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98babcccc5ea25863a9c318d604a4a42d3", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828851a03697c7774959fc416cfb94562", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c53a982008bf09e9dd4166b0951cbbcd", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98828e5f92ee9f4ec1748b4080490bec3f", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bf0ef22d2d2a7896a09d29bf0820a717", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804df066918b86238dcb7f48008bd105c", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffa51f879f144364aeb0ff3f57c607bf", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b003a58799e428f102c3fe814a37026", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845e5125f6ea3b38f3cee69dabad6a637", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f560657fdf89590d0f0380c8c93496b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883d426e09ff6b8ea756a05dc0943757b", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afa0f90a204cc7990a20e1b16fde32a8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829f6a57592a0f0daa45c5a948a312236", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efb627c10ae431f6e2d96aa1e5e4cb01", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847a61b3c7292cef0d0a4e8926e9cd9b3", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98975023f0b747ccee225f3618323c3fdb", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98817e00a76538a9839951537baa27f0f4", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c49a2b63c67bff414698a8d157576761", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9f7d1d2877c0b8555e517a2416824e5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98015e0891ed8cda691dfe3324aa6a3301", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98756e77604e746da6f57debc941a39c64", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804579d04ca377eed00f80d118d75327b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844171417c2c7734c7bcdd50fce7d08e3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4f170ee018cbfae19158ed636d6203b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7cb94218784bb1a4f1e40bd2325111a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a34227cdc0e77fe7f6da0b4f16083012", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98df069b81852286ae9c8b5f0a6d0e1a3b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fd0d93daffce343de4cc9e1d39bdff93", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fd9cc673a56706a5806bffa6a6a5dcec", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987b9ac416d79b69ca5979989ae6f20340", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9833cdf371f34e70a463a53581f94b5fba", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98770457296f09f4d83ac1e266b7df4d44", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982dde15e503f54d8338fd4aa350dbcb67", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f21c10caff9552eabc5bf6da16a09f86", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a6673e754e5c4d6144e1ae13abfd011e", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9828d29bf05e23faea45d59e43e70a3c0a", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ee0951283b94b1936704dc2587b17053", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98870209995d34eb78f12ad1b7792cc674", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98491d472dc1d0753e2dc57cbd4ee5a5ad", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984faa8c06fc0dd31cf03038456abc614c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/BarcodeHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dd88593640c2ad14c7e8275786540187", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/DetectionSpeed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980476035d3f93ac1e04680920c1723c23", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScanner.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d42ad3186a0ec4cdf437371e9731a00", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScannerError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9885026ce57960ba3fae91a8e00b000e84", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScannerErrorCodes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fcf9b8558cf1b623b044e95b684f5956", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScannerPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984312ba2f391e62eb9c1b6c5b98e54d32", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScannerUtilities.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9873b1f543bd3bb58ca22971d282e74cad", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987084c76f05be09916a70c3185c38974a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9848a9fc35478ce7edcf92345bfb66caf7", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8ccda0972d15a2871633e9ae19c5cfa", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ecb3693d280c7c657242bc950489b60", "name": "mobile_scanner", "path": "mobile_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989cb2e34f3513cde169ecfafd88e1c38d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859e1dce4125aac7479f8767295eec717", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853d9b168176c74eaf30c7edb8111c2c1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa4b54654cd7e6276c2e0efa40c4dfbb", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989bb5aa39435e1dedbaaa86dc47f937b7", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989129e93780548691a1c8d96b3ed4587d", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871f81a2fbe19f3a7925c73e53e1b8fda", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986755ac7b335c46ee4df9328c2206eb64", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c95d8c6247dbba846b1b3337e331450f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7a9c0f89132f8f4adcd9b0c45562444", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d97357401a3b6859bdb8426f50cc985", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb7541ba0bbed8d7cc3426e773a90637", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836990dac25f959c8ef1443886aa09767", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d57c824c433b499a4e2c22d6a3c316cc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98564d961bfe256f13b1fed1a101c01f89", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/mobile_scanner.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bbb9e1b81d97de613989bc030e896ca7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ebfe976228b454d2e5cbb94fa7e7a87c", "path": "mobile_scanner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b03b1dc5ba8802d54f7ad45cb61f98f", "path": "mobile_scanner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989c615d5aba5dd7c7bfc4ad020f519584", "path": "mobile_scanner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ed0b1d5eac347efdbea42c8b6b9e9e5", "path": "mobile_scanner-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c8ea5d4d5aceec04b6966d329fb3ae0", "path": "mobile_scanner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98353c0dcdc2f577b4ea070ab13e7dee9c", "path": "mobile_scanner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d6bdc321cf683201faeab99788a84d29", "path": "mobile_scanner.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989ad2e4aa9e2ddddc15257a64e982e286", "path": "ResourceBundle-mobile_scanner_privacy-mobile_scanner-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a4b95388c94a43f7a5caec158babe690", "name": "Support Files", "path": "../../../../Pods/Target Support Files/mobile_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cb4df16b02cfcebfded6a8025398a33", "name": "mobile_scanner", "path": "../.symlinks/plugins/mobile_scanner/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9861e122c01f33281fd885556bdcc4078b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e8dcb65e8355be40b3bab858b6051e6c", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800da2d3adc433b7fd398d7ae2e58a8c4", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808c0e483a1ff0bf021c0cd2ccd494593", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98562246bb084db3d843fccdbd1e34c48c", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b37a8f8cf24c9dd05ee6e6ad02ae6dee", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e73fdb65c60a1ab444d15201771058e", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895f6e4f092797ade29cf2fb988e8515e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808dd053be943b5838f8fdc137904eca9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3b959d3b721eb3251e44239f51448c6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b77b6affeb9f3ab9ffd0997fde2ca6c", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df4dc6b0263489ac1e01bd64d8dfe7b7", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af67f8b85f3996a78112ff29fff6c2c3", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ed8c6afbcf685bd166d1db2affa50cf", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849b9d36bff669f33035c938f9cea8d5e", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9807014d9d3c899b865b99d5a0a49e38d2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d6fa96aedcf58fd4426229325461ee69", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988714ceba38e8cf4050b3fe60cd10867e", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e79017e946020825b05fc37132f5d2cd", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bcdeb22ddeb45d88fd89c53f69c1218", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1ed415ff8a359a86ccadb1a37c69304", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d248ca8c4dc11dfce57ff6152e1dbd2f", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c7d9207f49bb8d486303c247a6b604e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b48edec2871e2af0a2940e9c02c1de7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ee39dc7444efd2b88235f53818178ba", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c486c95df9f8fd4b8cfe75b7524e621", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6f09eb35b3818a787bacf3512c39778", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a91164ebddab18e783a26ea8663b385", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98081b798971fd4ba2441c504956ccc0ae", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d9f4530ed748561f83983a57837ae22", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f469638672a06466ea869c40158a5458", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981dc169e34ba669d107c16312f8fe18c5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8c790d94bb0520d2405650a6a052540", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982add6f78b0aae3cdcbe5be11e654584a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbb9fa898e40ed7a4b93d450beb9f04f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846664d7fcb4cb1d4b9d43a8ebcd15d61", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986086da6dc16ab0179cdca4c992f0b314", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c53426da9615af11265fefdc82c83819", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b4fc0242c1c0a0cfe972b8546d078141", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9851b2badd9f5b1f650363b306fbc9b924", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9803971153d176cbd03745e898b733b3ed", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c0fb003f875248617079c6419b94d34", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98baf7f31e73063d9c1d6c5bb5cf313e72", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe3f17a82ba8b57bbe44d4818ea2a9ce", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c857309f92f9cf9fedc7361b93399747", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98add658324ae4345d052f69158d07264e", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e5d75a10ef181124fb6036f9c0d5607c", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b5fcb3cec5dd8f6b7791161862591751", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cb5246a388c861a54da90969d8e4afb6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836ce6989e209d150eac75e0a84722aa3", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0545c5876dbce77b90db88b91c587b5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ded8a39532fecbf37ccb0c5ea62d01c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980e631255aec8c283920b2a4616069ca9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98687d38ddbe7fb58d5d8dbb153bd3fc79", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98981ce40e769be505f7cd253ccc63abb9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea7e3c2e774c8d4737ab917365f24f80", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98481510a82f147d0ec1c44408859ea51e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6c8ee0dfe094e56358e816785a769ac", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d25f23f689096bc02aaa5f49ca0ce9cb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdd7749f51675d3d8075388ab97f25dc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f3905d0d9ad014836b8b40ff74fedc2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2c53c3176fed63a168a9ccc536535c0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e5416a032a12646176542257642911e1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f702e9d273fa90e7199a4a7b89070e4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f21542e29e5bb7bb8bd2c0b13a75239", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856ce4db9b5b8b6e8652ecea81ab5a3de", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b8e917c90b452739fddf31c84bc42c2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983df74122d6e19a9accb9cad1659bd7d5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98083d4ab23d9265c774919c6403916fa9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853081884cb7e24327ab4ff099bd8e97f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98884e7cfa53fa96f99bbf28d706dbb270", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862b29354327b7260c7d39755fbf30962", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862d8b05d872abfecb89300dc376c50af", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98319c68a9e58f733510f334290f1124a3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981dfefd5205434ca9b0850d0c844324ac", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f7fb8d91729a36562f04c71fcf8591b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98270062434e9e4a93c0c2ceeec138e13a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a49449bc2ce5f26a811628353533d116", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983efd035cd963e36d67c96dc0099aa359", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a60fd8a1db161de238bef966274eff26", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986fa38011d1e7c5656e832f6487ccbb2b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820aade4023746d015929f794ef5d7b1a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833fb5112d16dddcf3f4733db7d5d7469", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800003312febc07e79085bb3fbca9b240", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875ffa514bacc5a72e682d4fc6f4fe313", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877be54a09b120a1177c9c5b8771746b4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a17ebf3449c27d256dc240f760ba567", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc7ea1649fdcb300dc129811536d81e3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab90031f2f0196504a6665b42b0dd4cd", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98202c0f55a6ed409059bec5dd6dfb79eb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981bc891730735565b32fd878bf552c49d", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983fafa09e095b291b7b2f53879edd873d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984616db0100ed3c28191c6df9d13669c6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c11f693a759b4665db918bbddf8d960f", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d669becbaa58cc92400bac7b62c82359", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98eb7d3c156cd6dbf01c0e444d620a13ed", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ddee940533a97898061784daa12122b5", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d82aa06c037309222c9f457ff6ffd828", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98caa803390c12ca07abb3792e6fa85843", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805383eb03b3cb7cde437b61eeffb96c0", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98831a8cc252b96716d8f44ae41604230e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872d80976f1034579eb4f97a160264d28", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6282bcd4f9bd57aab047e2f13873906", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d3599e3817d96dfae54478cae947c30", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cb04d9b325275d364fad1d3d9ff944f", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863d03d619ca9c38514ec257960e95a86", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d53488b9ce21ca377864d67474512b6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98073385ae7b91eb91b09fe6c723d93cd0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ae30abaec6f5934895106ed6ca92730", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbd5aec9ff4a99bb713eb78b48ae697d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830c58d98aadad5773dfe932e6613ea49", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6b1c3dff8915c138f20f11bf804954a", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985b4b51ff504475796176485d3415b230", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e37627aca0550268a6bb4f96c350d5a6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f8b925829615eb9eb911c3ca7dc0b6f7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98372e0086c71ddb73bcc73fe6ee5bb0c1", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc77f649eb4fcf4e5dd6b147e4752ca5", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98310c9a989cbfeb196329fa2be6cde56f", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5a8c6e67af186420d2e9bd13526189a", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98575ddfea78192415a9b5fd28635b971a", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cb311003c0664f93374ea5cdea2f6867", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838b346c994131df87de18dc6d14cdca4", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986349cd4766816f8e54c00a4a848b8913", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9869a1229a6f9641ab75683c83d7d3948e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98731ef3c595cf93ddd0737ba85155c102", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da4a07b23bb52c6788e25b106b7068ad", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBlePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9848969a4e25b6873e513ad85e5d927df1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBlePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b687d1135d32cda1b6dd13b0e5e4fa50", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/BleData/bledata.pb.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988b64bdb1142a04fe332a26ff8c2715b7", "name": "BleData", "path": "BleData", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9899d79f2ec13498105b22b0c31e79374c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/BleData extras/BLEStatus.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b827198b5802c99edbd2442068075b63", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/BleData extras/CharacteristicInstance.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832a1e0f09596966584cf00cc0e7ac32d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/BleData extras/ConnectionState.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988a79d766c21c1a82a81ef3830ac32b85", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/BleData extras/FailureCodes.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98136e1bc4bbcefad5f797d2aee32f8af3", "name": "BleData extras", "path": "BleData extras", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987ee55cea16860f8525458573469d7bc9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/PluginController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ddf62844012f93c30d186f93eb4aeb7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/PluginError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9851f315187b741ddcf177c89399200aaf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/StreamingTask.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fea9f28fb116522e2e2acfdcb6780b66", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/SwiftReactiveBlePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cb10d6aa56659988deac95fba20c32e4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/Common/EventSink.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e7f11633b9a5ea7b1c39e38d2d07f708", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/Common/Failable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cc8f2dd9f8d0689f69cd63b9e02ffa59", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/Common/MethodHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989e8b0cc2a544d0c9c78cba08f6562ff3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/Common/PlatformMethod.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a32838f3eb5af98625666493efc95536", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Plugin/Common/StreamHandler.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ba5ab9b3ef098602ed76e80903e00f7a", "name": "Common", "path": "Common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d47aeb6f5abe319f8775b816f8007ab", "name": "Plugin", "path": "Plugin", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c132f82c4f15847edea779f4c21a28d3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Prelude/base.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef1ad2a3a7d80673c165596fe98b9396", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Prelude/CoreBluetooth+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e144816c27d178741337735b39530495", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/Prelude/papply.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985a6019ec673692acb360c217c9e1e33c", "name": "Prelude", "path": "Prelude", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988d1fe198219225a934b13ed1a150f118", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Central.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987b2fb13155e0bd92d8bc05f9dc322bdb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/CentralManagerDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9852823ec9b5ff7b06530400f0ccb4e468", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/OnOff.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb608bb9acd3420ef2547e7a425f0151", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/PeripheralDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987c9e3aa414da7da384082dda34a2e5b2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/CharacteristicNotify/CharacteristicNotifyTaskController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d8b46a3297575711e8120cb896e206d3", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/CharacteristicNotify/CharacteristicNotifyTaskSpec.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985f289cead4259b1eee5fcf1aef0812ef", "name": "CharacteristicNotify", "path": "CharacteristicNotify", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984b6e644b327470722a524b24da09d5af", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/CharacteristicWrite/CharacteristicWriteTaskController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98788273bb913fc9c227a09c9d89bc7d33", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/CharacteristicWrite/CharacteristicWriteTaskSpec.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98684691ab77f815fc92b6f2d7be8c039f", "name": "CharacteristicWrite", "path": "CharacteristicWrite", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984574dc6f8f771462ff71e05b3117e3d7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/Connect/ConnectTaskController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e52182e780c1e12089e87ebabc23c03d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/Connect/ConnectTaskSpec.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9840efbe78b56ded1a864505513d3371b9", "name": "Connect", "path": "Connect", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980fa07cc8c90c0c84414a9ed5ffafacfe", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/PeripheralTaskRegistry/PeripheralTask.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9803a56b2cbb8d85dcf4edf8cdb406b7a9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/PeripheralTaskRegistry/PeripheralTaskController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986dddd9f251be0d03a610df4e8bf5473b", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/PeripheralTaskRegistry/PeripheralTaskRegistry.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986095c00f59760091d55041307839e670", "name": "PeripheralTaskRegistry", "path": "PeripheralTaskRegistry", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884e7218dae637efc75e7879467ff9e59", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/ReadRssi/ReadRssiTaskController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989f4610790278b80c818ba52a4299d7b8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/ReadRssi/ReadRssiTaskSpec.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989d7758cf8ddc470e4003bac61364350f", "name": "ReadRssi", "path": "ReadRssi", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2f6495307506d5fe9664ee9c31926f5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/ServicesWithCharacteristicsDiscovery/ServicesWithCharacteristicsDiscoveryTaskController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a0c9a09fe373a00cf247be268077e8e6", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/ServicesWithCharacteristicsDiscovery/ServicesWithCharacteristicsDiscoveryTaskSpec.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff837e04b4277c55e3b80eb48bcce42a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/Classes/ReactiveBle/Tasks/ServicesWithCharacteristicsDiscovery/ServicesWithCharacteristicsToDiscover.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e5a2afd3ad9f6404a6f9b7ff5689b4", "name": "ServicesWithCharacteristicsDiscovery", "path": "ServicesWithCharacteristicsDiscovery", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cf1a2baeef71ecb6cb4f62193ae5a35", "name": "Tasks", "path": "Tasks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e07dcdb3ffbfe3e80c5155e25fbe0dfe", "name": "ReactiveBle", "path": "ReactiveBle", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860343d6e0a4863a4fa85f832ad3903b5", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a837d31cb38f08c7530692a3cd1bacd", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98faa055841864832f81c293cbc169daaa", "name": "reactive_ble_mobile", "path": "reactive_ble_mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802ffc628e1a9cc452431f2a56c949e5f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc31eb81cfeca97d888e34dabfbc03cb", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cc98402b5206b766e9528b791492eaf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861006e9335ff69fc52809ac2ac58d67b", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834e07da95f94db1faa98f0de650457ee", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a653458363b0491077be74f5ab6b0ce8", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcfc5b005ab73c70139db27d3575673b", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d3765711e3bee3067adce5a41078cff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdd7fb830ae7a0f4eb113efd1e8b2efe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c7450982a6b8e835bd03a41bbfd5f4b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e090b5bfc873bfa1f6947a2422ef080", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0a6d14e75664ab8e63b3fde7ba23c20", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bec1f79b388d2a1e2a019db5f2f3f061", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b9de1497183201b4bca799ed68dcf80f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9886244ca22116a61ce617ac6f9a9155eb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/darwin/reactive_ble_mobile.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a22a5d6b8db769d63c9b3ba2003a4414", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9846e7f9af23ae15b56d6dbfcbc1ae0dc9", "path": "reactive_ble_mobile.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985492e944799208e83287311afe95efb8", "path": "reactive_ble_mobile-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985fb56e438f1c4054d5449240749ef6b8", "path": "reactive_ble_mobile-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804c6da25f55ab3c69d5f613a6906f02f", "path": "reactive_ble_mobile-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ee423265dae3e2b5d7e47ebcc016020", "path": "reactive_ble_mobile-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c82ef84b5f04107aff549dd3dc15c932", "path": "reactive_ble_mobile.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98074ae605185777d2f7d3972c63aa97b7", "path": "reactive_ble_mobile.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f47428b7b5fe76a3831e3f9fc8e42c60", "name": "Support Files", "path": "../../../../Pods/Target Support Files/reactive_ble_mobile", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fe8ec59d77a0cd415e1fcd85282b09e", "name": "reactive_ble_mobile", "path": "../.symlinks/plugins/reactive_ble_mobile/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5fde51a5a9185494986e1863276997", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98df3a67a35a27cdb12626132d8ecd36ce", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98692cd6323146bb6367da8e90986769d9", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861bc31cb6490b542c42024a0479ea8d3", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f78ab4d378c3cf4c8d92c0342d26b20", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98139e1e032eefd06893350a83c00a3d7b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f528de73ca80241dfea3c40a3fbe646", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860f6594e78aaad86246015877eec6f33", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98320bfbc42dc21db04af32e7f2496b234", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cf3c49d9a78044956ca7909a345b345", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb3c7a961d269c38b5869f8dab7b027a", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b32c4a0c70edd6535f2674a7fcd3965", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830e7b73ba50f0ea9b7116370895909c8", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818c26d398fe7a7b9551ff65fc0b3d564", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ca82dc3338e01f430466c09c221d73b", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db6ea9f3e85d71c5349e089951fd9e5a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98add3e13049634e7e87e9ab6a021289dc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984f18eaf572f69e0371ffaec5d7b9efb2", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a45ce35ebc3267300ad0bf20ab0a9e65", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ad7f612c2b32045d870dfbcfa1e7851", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880a952a6bec155a07d9d217b2f16ff65", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a749f7d186a3ebfd5a575d6106bdbf1a", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d8292d609bf532d3bd6872a816ed9de", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5e44127101021a11f96323f328025e9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6c76f8188c42f32577721b09170b232", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826b0e28a050c8a6255d6fd622384ec26", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826e6e09dc6cd234fa503a9ce8b7b22b1", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3a59ea8a44b225a7773b21299bbaa75", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcb4166364437944302afb1680137d16", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f57ca309605f8ba1ecdbe328149ef049", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aecab04c981105736c7cc92fcb539b3a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6ff7753ac4756eba2e0afd139398d90", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98caec6e25c773697f117edc554df6761c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ef4d4da9422b832f7e29c9e29edd01d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bcd29276b90d95337cd2685ff6f5741", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7a60754add5fb9975e94ebfa740a94a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dab4f3958e2b0881ec6866306aa108a0", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9822a4d3442097ba5b2461fa1463674527", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9891e9f0b637787f8960a3352c76c14f31", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9841fd71a26d1f248f4736ba2358e7d419", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988f97a59b6904b95410506556cf836a36", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ddb55623cb8d93ecac3d859ace3043c9", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855cde3ed7d2899f1519ff4b23bf277ac", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a8b857abd45d4a64685d68742ecd3c06", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d8a006e706426857cd09967c7a627a1", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a71dbf4fce166c964260ae9af4f6010", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9845731f959c130a5127db6c9716ada9bd", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9836a756127d44f483fc6c9fb7a148e700", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e4c7b54d6ac94b3a743bcf0639086106", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989117a9136df8a72b5084a585fce42661", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98777b1508e8fe8bd6fcf50e36a45fe3cd", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9809003228a63300901af6477a2f53e478", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb0a96e8d099e2af339345350f0e807f", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d21e91ec32b750a90310cf074353e49", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b26a8155ec09318ebdd43582ba0828ce", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af9ffc707c1e44252e0b583e9fd037e9", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ff18970f332ee3e7725a5df4d223e1c", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c0ec50156191f72de32707a275edb48", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98370a376cf5e45540e79a68a7c2b6bc1d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882da80898b45f101e7604fd90a166095", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e3dcf4fdfd56d5c2237c00bf0f07762", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae40bc427e6e1cad769dc78f0306f071", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874bb1895a21b979154bf325d8becaefa", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf937583fe5b79394aff47a6a046be01", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af25c321aa8fbf5c5b27150d4edddce3", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b64066114ba89a489df2f73bcc0d087", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981c916f3663a84d6756322b04aff6082e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dfef45f935f722077c3b02cfdbba7b41", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b49e0f304273cab536d681b6c509ef2b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9812f42073e8c7b8b435060d60fca32d9f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883941fe199dfaea007dbb1bdc46711a7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987dd962d06bc86f5d81656be476381554", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a83b2f9c300a405c85f49301710c4bea", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ced952e8ee070dc1da270ac17faf464", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fceaa8a602d584508d2fa243758115ca", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988066d430f84e954981acd8b353904d37", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98108dbf5a7559097bdbc4c21d3f3d4223", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98381ed44aaeb93b88c5d8ff0932232800", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866d64661c362667041036cfce2382cfb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4b7e554e3867ed15a3b1614a64efbe2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca2084805d7fa0c25f21ccac0834ea68", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9889140b1155ab260f8a8d5ba8b258ed38", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98488afe852892b543aee41b0e9be2617a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6120093d6a7b648531d41e079efc863", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c32b4166f8be1c658bbaf84168ac7e7e", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cbbccc7573912d936dd69b02bff7068b", "path": "../../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9801e7da9a3f1494c7461078f34771e6ad", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d41b582cdd254cb234f3309a603fda94", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988af9446855c7fa0920d9191e7a7cfbb6", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862a06e33e96fc92de621abd3287eb53a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c2b6f29d0a0e7dbdcb8597265634856", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a238650992c26c1c8e0ad78cd407cc73", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f92cd7eed122df2ed7574bf2b104828", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4d07c24359b40e66c9203fa22fd70fe", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983cc31fb063071797c8db9a60fd66a863", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d2b08aba65ea884a445a2f47ca264b6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872dbdf1d399818d2b220d22b0f337210", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2e6389787e2f2c67bdb8faf804289ea", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac4448e779e8423a44d7a001c15d37ae", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a49a5f5c9b7fc6c4f9b13d851c52b83", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af1d19768de17f810f6b759f4a88b6ce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e270c8c758e57db750567d6dbb12cd33", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b54293fb3255d8a80af0b14bab2dd0c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988caaee1c779b0e115fbf1f90c2c8cd3e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894508cfb6f904d9462004f974717ad9b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dd94508602202d4cbfc52d6e2b975d5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c67971043f151178a419ac8ea15327c8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870089cd39a3f2e5d796a7913fb38838b", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98bce17b59ef18faf2e72bafae0ffd938d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e980dbbbea0983a88f6a4a6b74c38b923aa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982dcdecb7cec050046b53114205eb8bf4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98763182ef490e765f7a24d9d0e7b7fd34", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9864d0a72d6f3d07b166be48ea7415814d", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98886ead8254f532bb84e03abee10c3ff6", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cbd7723a95381fda50ce90944e025595", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f90e20437f45fd036021bbf118500bec", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872c48a88c9d9e483b46e3563dcf2cf1e", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e93d91752a0c9b04babdfa72c556f15", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9821ccc76add708a875ee834a317f43d62", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9806214c960d1dc7fd3c01b2a8106c3930", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98be8b759875865b32eaee34fad14fe766", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0f0c2dce3b2b1c968139f302d2535a1", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98db20344c748fb197e182e4da732ab36e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9899fb350b33d80fd548bd6d0787e84d4e", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895b47e68324cee89c5b3b52d970973e0", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ff1e8459a8d0fc82f34d7d25c5e2c98", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ef8f07f37e0b388cde2523fa8fbd500", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c45dde92e671d46f17fe546a2836a5d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987dd887659cc229149bdf6a2a515abddb", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985280bc5effac0c52790d31e7edc3daf5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807bd72913a777de14f2c7ee456b45ef8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a0864847a33f0b62a618c5aedc174e8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7b613e813879ca4542cc1da250a271a", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98504ff2784e75e17a3fd5818491fe757d", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cea7ec4d1bf5d8497252b5d3299ea0bf", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98563013b852508797c40c4fd95d6ebfa9", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bce44fba9a35c075af725230ee8c291", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fc2b99cd8a9c7415bbc1e22fce262eda", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98830aa05b3967bb1784da74cee09d2e19", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989941cc0b491b562d74464fd78aa4560d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989ea5dde595dd8d2321773d5de5f30454", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987fc95e8ce3587492f04823e75f8d6c20", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832407bd71cb2c1c02907b5d589b60a3d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858eb8e00e978092a74b7ed4319b0a188", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea2a927b46a4b8b2c361caeb3a2606b3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0715bd6ab16bbd4edfc447a4bee35c9", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807f2218d3a3e9d7250a34e61f39d7c0f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bccd8b49ebfb7117794b1011396f5785", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c6ef7b2c7328b2d3f447b9336600094", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbf5e383ed193249455215ff2811b67a", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986076b9790c67c373427984825d9718df", "name": "web", "path": "web", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c293b3f95ebea10b1b710f40198c5cc1", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c3883d12a0dc3338557711c5ff55b96", "name": "somecode", "path": "somecode", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e1412e80e8549a3010469903336c84f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6a9b3577702e1bfb13785913e63d9bb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7e2c72eebf0231c1218e0c902683c90", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5b543adbe70a38970b00b79ac5d4c5d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987efbc8096b77204aedd9fe49f239ecdc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f6d41f2b155195608128c74bbb2efc5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981aa2426ac4d67ac5f349c5b5a1f1eefb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98210dff945f379d938928a4abf4d500e2", "name": "..", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ef85417f857f6169207911c9fac4e2d3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e18d8877b6ce182e84de47cbc68caf93", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b0f353d239e468cd701cbd122bb0fa07", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9891592460e6cab669e801aec62c507ed5", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983080890f0eb0b0deed1adf879d9f3e9b", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98061545b9be840e4152f08acfa3d63b68", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d25044494b49d4389715008385f7b6d1", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b4d2ebd3b7df5c88d269507b6126b4d", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983bb403d682be0835c8da2ab106bea813", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cfb5464b73b435dc9b80292c065aa6ea", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98baaddfd147090b71cc7a0222af363c92", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ae5bed7388c296348563ebf190d2867e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c75dff738c2c23bc3da5b1af34632e28", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880f40a44f840033d42130bb9bacbd811", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e1a8d1c5790182d4835bfc5119136e43", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98065c2ae3cf0eaf2e496887c21452dc00", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreBluetooth.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e983a82eaf2f28b4ed4b254c53ccd8f5f99", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreTelephony.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98d7340f73f00e5c96b91cb13a0391d8ea", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/MediaPlayer.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9870350cda99093f910f2f9eecd3826fd9", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e986610c408657e64db4bdab3f032e5a171", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98da7dd3308e62ff3506ddc7ba6752920a", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982af92348bf1192cd88dc278de42b6296", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98693c8b6afbf1a93971ffd810c5b62d05", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989cc45bf1ae707b398b9b759319ee5fa8", "path": "CoreOnly/Sources/Firebase.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e851b4b0b3fc7b22a207ba99e791111f", "name": "CoreOnly", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98757ca6eeda67365ff2aa5f37b34d9622", "path": "Firebase.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ef3bc7dfdb6bf74925d320c5c0d21ea7", "path": "Firebase.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980a225e791b95f35c52e6c1b16badf973", "name": "Support Files", "path": "../Target Support Files/Firebase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861b6ce9f26c09962926b736307468403", "name": "Firebase", "path": "Firebase", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddbff2fef2834f8070a9159f6fea9f92", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988be8a4a93d48d1e0636c5cd3ea5dd09c", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98770a0339b5a17426fb7af9493ca8698d", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRApp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836da1b95ee19a2388c62618c47abe53c", "path": "FirebaseCore/Sources/FIRApp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f0c6ea79c9bca8c4cac5318e9469487", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98452ca98c60c1ba812eb98617d7edb953", "path": "FirebaseCore/Sources/FIRBundleUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f2125ece11c8f5b0dc53451a5ae454bf", "path": "FirebaseCore/Sources/FIRBundleUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819780539fe3502c422abd24e4fbbc93a", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd44bb2bd47ef1b763c7f77f978284d4", "path": "FirebaseCore/Sources/FIRComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c827e374ff0c7662270a34fd75a8c523", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98457b38747647fa2d3d99e0757a4e9b0a", "path": "FirebaseCore/Sources/FIRComponentContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985223d4e2c0ae0ed18329488325089e25", "path": "FirebaseCore/Sources/FIRComponentContainerInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b91017059480845cea251fa72e4a555", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876735c6247cb2f4f49d8a167fec14d22", "path": "FirebaseCore/Sources/FIRComponentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9899cd0378fde5fd94a80d33fe7f6b6441", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d5e81394e8d4918e92c243946d3e874", "path": "FirebaseCore/Sources/FIRConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98802b7e340fd1019a350c4dd2a3f2af7f", "path": "FirebaseCore/Sources/FIRConfigurationInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a618d6753db75c9a745a9e297f8242b", "path": "FirebaseCore/Sources/Public/FirebaseCore/FirebaseCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98111aad78c3927b211214a05609ca8c5a", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821e54d54079310f31abb07efbe9b8841", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ac267ed5ba8d823be38c7d574e56bc5", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7dfe792046a0c2c053922f5e9f5b4c9", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844ec6969c04df020077aaeac3f0b9667", "path": "FirebaseCore/Sources/FIRHeartbeatLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989da48ab4731506da281d899ec7c69f80", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9e0625997110cd12fed3ba921a54cdd", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981be08df0e0e21099f187a46c29d5abca", "path": "FirebaseCore/Sources/FIRLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980225bbe449672c4a2d1bf4c421fd45ba", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRLoggerLevel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868960bf48a81dc0a6b2a391baa945707", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIROptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9831757b62d562dd4ad2d2f9f705216a43", "path": "FirebaseCore/Sources/FIROptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98830a09b579e790c0082b355605a3cd00", "path": "FirebaseCore/Sources/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f024a5fa20206ad1b67b27f3ba6b7695", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRTimestamp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c3c2cf64cdf587dda98459b2b98b683a", "path": "FirebaseCore/Sources/FIRTimestamp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cb194aee62e1b9a981be42aea04651e", "path": "FirebaseCore/Sources/FIRTimestampInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810d1da66416368daa53f6fd9644a757f", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRVersion.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b2ad62583810258cca9ce7d518a9ffac", "path": "FirebaseCore/Sources/FIRVersion.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c67e1dcda40e0e6cbadd6c6a0247b52c", "path": "FirebaseCore/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98036b8597d414e1aab7ec67e0fb76425e", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986f4032ef6a35ed3b54c37ac6c32ca9ea", "path": "FirebaseCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982106fe8be9807fec098bab8c1501908d", "path": "FirebaseCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ce69c15710c1b8b0c83ab1953bc36a15", "path": "FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eff05d231fe7b3c93737d59298bdabec", "path": "FirebaseCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987e91c425d83be4e5e1c4500537ceeb2a", "path": "FirebaseCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9831b14aa04aa3a142c876afa223adf451", "path": "FirebaseCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989f82f0aae8d8cac4908341b40d089b4e", "path": "ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98da69a9b88ac348e38271237785ef4320", "name": "Support Files", "path": "../Target Support Files/FirebaseCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e386b77a7d171076baa54bec3cd66577", "name": "FirebaseCore", "path": "FirebaseCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9803d8f1bda4e9b0059f91ad0fab9458e8", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fe9b275a4debe62114af99e27d3ee65a", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98731f7f1bcdf16cd0241c294bcb0d91f0", "path": "FirebaseCore/Internal/Sources/Utilities/AtomicBox.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d103f407dfd020b42d690557712a443f", "path": "FirebaseCore/Internal/Sources/Utilities/FIRAllocatedUnfairLock.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98465b3fbac5d828122f332e587c257301", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Heartbeat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c6307c9e5162d7cbdbec5678a528cd4e", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c753aa215a35ce2d1a2d6c2767548815", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatLoggingTestUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d409304b7ff189ca903a917becf1cb38", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsBundle.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98606595f101b6870bb2cc2e1673ca72a7", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ebb55cde07ffcb8065d854abd3a00a6a", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98595182a5ceb9ce5d6b36e93e38eaf9b8", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/RingBuffer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9899948d0fca2027fa542ce4af1df0232c", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Storage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d23cb33ac8d5cc5ce12c555d86ea4b3d", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/StorageFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aca0182c34b0dbf8ef29d377b42bc35f", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/WeakContainer.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c3b41dccfaf034252096e691b17a15ab", "path": "FirebaseCore/Internal/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98763ee9a20c73f31e30dd91cda5a89aea", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98058cee708d797bf28e56dc7af526808e", "path": "FirebaseCoreInternal.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bdad4f7c690ac17f54b486f3b84a2ffe", "path": "FirebaseCoreInternal-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987c45c04e1ddcee89f8be35ef8e04f0ec", "path": "FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986eb01985caa7ca28acaaaebe20765b24", "path": "FirebaseCoreInternal-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a7b69cb0332b6b7a65afd84ec28bf63", "path": "FirebaseCoreInternal-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9867979761cce25ca12169b6d648375039", "path": "FirebaseCoreInternal.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bf1e9d49cc90b45b8219d408b972f9b3", "path": "FirebaseCoreInternal.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98496dbd67acf8ca09e4040d47d7baa8b8", "path": "ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98702e8299860caffb04224553d021a4ed", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a95a8b6951d9209d0db154724907d7ce", "name": "FirebaseCoreInternal", "path": "FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862d3534eb10f345e0415e1b6e8f83251", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983037b5d5dfd92581186e7c2219439555", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b526f22c4cf4c4bd6986a0d99c231030", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8eb43c4a00bdba2d271244ace6d3608", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8dd6fe1a925c3f64c524a1d7efc5e78", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b9c0903f7173cbb41b93ee9ba79b6435", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f929929538f80f33d9348fa900048de5", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821a1bb2d86e0dc495dad9567ea325c52", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FirebaseInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987410ecf5e371a501b63cd308d6f454da", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f70017089e234cee622aa8ccd6c69ffc", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842cdffb768657780c29905f5a0170716", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad2ae90a8a45c7e03e49d42bd41cdfb8", "path": "FirebaseInstallations/Source/Library/FIRInstallations.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d78944b5fb01a88663b2100edd1a3e1", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a6d65362fa2cf0f0aec3f6332222b43", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dcb6288528acbda10f44e691a0c34d76", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsAuthTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988202df6026927e4d6021457112b61830", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a22f1d36f4578c2d0ca4e922dabdd277", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResultInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d451f48c79600c463b3eb91b352a81fe", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6bd25fb661384436cb6dac23e75c529", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af07accad84f11ffc0d91d2f01490201", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b4cd15b4a262276ea1325b271915f6e", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af430ede085d84f9bd1497ce1f8e1fb5", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98733ac1b862b19045c6bc6bc2b3a7b0cb", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981dc2042e28b43ca30f9f84e1d24dea0b", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f8df6f34852573c55915652c1ebfab36", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98396ea6cdefe5cce701709dffc54be193", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1bd707eda9038672bbb521f54ce9725", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980077f02a6f9c119795f5b0b1222f891a", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ecc25e7c89eee28508b3a5af41948239", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c2cc2c540f93cd11bec5bd309301137", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d88019211758474cd6b3c28bc08cf4e2", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ac4e55b51a598bd5747b9cb553e0788", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf179bc17059b747e270dd2b1834bd7d", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98856c737a4e5349aad149fd6dd1cd206f", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983339b75e48f098546722958fce8abe7d", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b38fd110591a9662511a1bb2d8b62486", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d11d1c1cb03767f0c7ac0893a6ed91bf", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98426f76d1bdbca6071cfe23e0c48a349d", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f73cad4c989f28ca2f22727947597667", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsStatus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848a765c9f46feefd017ab31b25db06d1", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ce980bf64b3047ac12379e2d5f971e6", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983216e1777c37d52a352a8cb6cc608b3c", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a100ebd4e5ee506cd3b7b77481afeab3", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4183ebf41f757048748fd4c7eb52e3b", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98635f30b5cd0846923c972f55de5ac7df", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a349a668baca78ce47f752150a237029", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820939d60e06f777de436bde912dd4549", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f752be5cd6c1e9a65a6acd861d303724", "path": "FirebaseInstallations/Source/Library/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d1dd213d50af910f5b716a14fe26d6da", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988febca46f3aa1da473ced34fe5451306", "path": "FirebaseInstallations.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9834dc014147202559fffceeb996edfa3f", "path": "FirebaseInstallations-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989ebab58860dcd7f0c0e2571cb25ee536", "path": "FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3c12fa7e707a1116546371e8052d2b8", "path": "FirebaseInstallations-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98027086a0a91cc749df31db074cfafe36", "path": "FirebaseInstallations.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9819d68cf8abfc37da4c4738d9448758f7", "path": "FirebaseInstallations.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9813b6731bcc2fb3e35d27069e621d475b", "path": "ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98758ee9e803f569743036537d8ff3bd17", "name": "Support Files", "path": "../Target Support Files/FirebaseInstallations", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8126afbeec800529b3f7afbf516d44d", "name": "FirebaseInstallations", "path": "FirebaseInstallations", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ce39c7b58246d54b92e6b4f93b73b32", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc4f0661fc3c4db89e61311785d62525", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b7c1bd6569893d5493c327f0b17cd87", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d86f2865c3ae43cf61edac1d10b2e69", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98192e51856a25a05490bbd7547f1033ed", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988aca9d7d06ce13a0b68e5d0a521fb24b", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d410b0399ffb56fcacc45fc59a751385", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a47d2f840d64922a401d641ee5991a50", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a9e79548097193c086939bd12c0810a", "path": "FirebaseMessaging/Sources/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9873b05d0b227b5289118e5f42a6d88ac7", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862fe7b9e2aef6f459a0165cd98413193", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e9e4be7f2c0220d7eb721d51897b372", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1f408d821585ee20fb1c96a630b3004", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98adeb2806bc439fbee44482da30ebf874", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b913487cef6444054e19722d682b4aae", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98458396667e381117e3faa8761809fab8", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e3895031d86319c486167cfbf1e1a0b", "path": "FirebaseMessaging/Sources/FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b1d46cf16426726b1ebcfa9c3d8172c", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging+ExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98363f957c2a3771a8afcee082f384ec9f", "path": "FirebaseMessaging/Sources/FIRMessaging+ExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db0b8370cd94c7def04791a5092629e4", "path": "FirebaseMessaging/Sources/FIRMessaging_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810f6503e3aa3997e3c23df750b6997a1", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fcf2f870fbbeb113d21d1b1624adba60", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a573ad8cd2df5df7e77bdc289f6de50d", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985f91739978d8cb0b2fbe54b9bc2cc642", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce273f5d0a77087db3be761ba49ff4be", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98226c3965083e38f2646081cd4d474e0d", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833061ba6d72e6922dbf0c676d7d1464f", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8d2db4897efb94363532cab66b89186", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821da4df9cb5e836d826a3a1d14eb4002", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838550024b8324b041172a12d2a78f2ff", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830f32ba2c6209c138a5dc95bed6c0621", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817984e8641ebc2a0e36c7e840db01ea6", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d04e78fe797cb8f7b6841045a16a758e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a273b719840134aedff8732a2fd0e534", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802b1a16e082d63230a675727a05902d8", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887e2a05232380e83245789ff32189232", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98734e38732e327784bd20e2abb9064261", "path": "FirebaseMessaging/Sources/FIRMessagingCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd50c75f3abb2b2612724c6519d541f1", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f699e5c59b72bd661459bbaeb1ab9a1", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af3e69744c2fe5d8424e55507dc8b7d3", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846b01d22c12534f73d2582ee5cb3bd88", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807a38ede072b33b844e831ea83eeb615", "path": "FirebaseMessaging/Sources/FIRMessagingDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e871fc4716e307eabddfedb39d52516", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessagingExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af85b6cadff2bcb3e985f218c9b487a2", "path": "FirebaseMessaging/Sources/FIRMessagingExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2ac2a1819803afc1dd95efd6a7406ff", "path": "FirebaseMessaging/Interop/FIRMessagingInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9e200327139a863f74b90836e5377df", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807e5974cfbcd3e34c08e326dec9bc899", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846a6d39281a0c8622c57b2c07a1711bf", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd0580b018b810cc66538028cf5e72fd", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98365abaedcabbd5fab1b754fa6f820cb1", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9851aa583f16bd2e5926733ccd4043272d", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5dcf38979bbce6e0bce11a7aa5f3146", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804a91a8045a38771efa65ea06417087e", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98727eb1d2a1b635b539d99b8593a3ebc7", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d9f2465ec1db9da823690b1bd37e507", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de4cd44dfd97399fbec64d1626451824", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b641754574b484f6c1597f43b81e0716", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857a09ec0e7e96babd06bedacc836d128", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98364205d8bed6929b6234b95511926373", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba42ddcfba9b96d429b6ffa6cbd5d8c0", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984604b6b0de73ed0c99cd1b86a193fe8a", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98958cc767af7c9d40cdfcecd13893b512", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986d1721b1c6142fe98134d44df8771a64", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e78ff5ce60a1734a534db9be538809a3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984c2240993554ffa780b33f32e8a97ffb", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98851605947b567a1c36f8709494a740e3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ebb0c1844a0796f90aa6123825320fa3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a67c50e96a97bb252f9f4a4ae60746c1", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984ea15516245e4d50bdaae5f5da952dc8", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870adbe12ec722c18466399f73e85319e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98df83c3606738d5216f9fac1b6a6ce6be", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98198061019db64ae3c681b6c7e1d64a06", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ad636c51f7e0f1f724380d1dfc2661f", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbc2dee539351522efa5fe2f9c457a77", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8396d774c7a6a07e6f63959b1908959", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc1f68235b4526146e422c3e8f11e59e", "path": "FirebaseMessaging/Sources/FIRMessagingTopicsCommon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef9a8f0d894b230ceee220b0cd89fe6c", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981361c590811e9f1bc71a51fb14367c2b", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987aec98ef79fcf6f69eaaf04a828c11a7", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984902ae389e544c07a6dbadbefdb46326", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c38b9cf5c1db1d92e85dbc3c6f92d62", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8599e87d42e0da6e3a84b5ba7284fb4", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0180046f1b57b2b36c47fd9e4648d34", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a7c2737b30feab306e3ef71e994825a2", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e3fc2698accc03d1afe832db54c6df96", "path": "FirebaseMessaging/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9849e2177b26c6ce28699da2e5d1c84139", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98500fbfc8111fd5cfd2e3d1250d21e000", "path": "FirebaseMessaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985095bf5aebb1712c6c27ba2a84d7f099", "path": "FirebaseMessaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985dfd7bd17739e1873b6190fdc8fb4a4f", "path": "FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987dc69c9b0ff9cc2f8612f69c78543c3a", "path": "FirebaseMessaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982cc962c5ebdc9f6dcd1bb3a6f9f073bd", "path": "FirebaseMessaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98433dff4eb1cd8aae37e56b0bbeb5bcef", "path": "FirebaseMessaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9856e15f8e21bf341cb1ded0c4f749668e", "path": "ResourceBundle-FirebaseMessaging_Privacy-FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989ad145c7d985e3705987d79bd3b8bd33", "name": "Support Files", "path": "../Target Support Files/FirebaseMessaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fa35e68b4feedea1992930f6a1a857c", "name": "FirebaseMessaging", "path": "FirebaseMessaging", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824d40d0a435154e6fd7d2cf89590d2db", "path": "ios/Classes/Flauto.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e989795b70951bdd04f1b19a90ed6a962a6", "path": "ios/Classes/Flauto.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8acb9a784b6f126937b674c23b3a4ef", "path": "ios/Classes/FlautoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e98d0fa4469ea2eb15b9f454ad0fb897b34", "path": "ios/Classes/FlautoPlayer.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d4b97ce3862d8e8218d5e9a5078fd77", "path": "ios/Classes/FlautoPlayerEngine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e98d854806e959eda74b3a15b5c4d8f17ed", "path": "ios/Classes/FlautoPlayerEngine.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9f939c2f4957126ef0e151c5092371e", "path": "ios/Classes/FlautoRecorder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e9880890130c623752d2a03539a31b14776", "path": "ios/Classes/FlautoRecorder.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dbdab10d94d29ae2632398ddbd2c5f3", "path": "ios/Classes/FlautoRecorderEngine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e9893fe7be1bee59ea21cde41c66df1b6db", "path": "ios/Classes/FlautoRecorderEngine.mm", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98253c75d0d9a933238febfb8e2ec65d70", "path": "flutter_sound_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809c91c1c5dace11d079323a2d1f6e946", "path": "flutter_sound_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98436bb71b034237017595c921e0ad39e3", "path": "flutter_sound_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f1e7712d9dcd97ff080448e9e1ba70e", "path": "flutter_sound_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f461441ea3b1f377ad322766801775ef", "path": "flutter_sound_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989b690959e0c4f6182b00c24da5cfa11f", "path": "flutter_sound_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a0763413af8ccaa9d98ede86abdc5d44", "path": "flutter_sound_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9848155297795ad28d54119bf14cfb2535", "name": "Support Files", "path": "../Target Support Files/flutter_sound_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884dad986462837d204c00bd4322f1090", "name": "flutter_sound_core", "path": "flutter_sound_core", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984cb6463dcef9c9343394fc9a8597bca7", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/CartesianPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bcef894bc3538203874ec8284fc4529e", "path": "Sources/GoogleMapsUtils/GeometryUtils/CLLocationCoordinate2D+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989dd38e93d5c0e61663fa4d17a838ab25", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fd86cade00ea9aea6290c51dcb63328", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d336d36f970a76e27273562d78905df4", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPath+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9896a3ecb7537e6cec3457bcca56d5e5c4", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolygon+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981cd40bd659ee9548bbec8ba9ca1afa9a", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolyline+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef78f96c9a6b9395904f42cc0fa41c2f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d26f886d12ca7aef774593f80861a90a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a3be53e170ba78779d522d81f16f916", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824f5a10278076bf0846f788e28ce63fe", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce694be27a948b03d29958e93930560d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b92e92404fae88689e8b60455118f4a8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3579c9880a96556fd931299b6733f14", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806ccfe5008bccd4d47af5605aeb574d7", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2af144c2e9a81bff6442f14b1832d33", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d58ed07172d042a7798bc5ba0905d61", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0c89e3117d86d4cbd6b5ab5cb454a70", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f08a7497667a13b53434a34ec831df9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa166cbc5566cddcd2f52f7dfe6eb174", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a725e83230d2fbf1b9b93bf78c8607b0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4aaecea7d1c30a5ff8c090801799ddf", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875d0a51a230491a8cf73a6133a3d16ab", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c530d58704f1e4d02c9cb18e6de9c0f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f445fd2c5bbaeeae39f10bc76921233b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7dab136baf1c14a9ac9186128d87377", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986dfd24a8fd1ae14ce42f9f0ca2bd91c1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985173d0031f50e9e7c679f494772eee1f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981393137013cbc2f80f94829112d820d8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988086383941e830977a2bd1bb96d5a269", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98171bedb83d16fbf4f93798cdafb08edd", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed629bc59e984370a8bc13b69516d9f6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981bc49f47b6d69fcb21f495bcbf4063e0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98686791482ff3b35c041fab8ea7591d12", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859beb17ead4c842adbb92c59295d4d08", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce087a383d5c4319377ee0f0b1aa2a3f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811b2a421ff2f9d6ba1b446a95524f62a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9879ae1962f65191f1eb658b7fb290a21f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c22d1fae2bc6b0d57dfe577d14228b3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9847b24d20efcb5aad8b3f44f73c9dcc85", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ae958656bb7764d516b3ba173f71d40", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98018cb9fbb52872e1f76bb6ef6ed6b6a9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982e0241950a95ddd07d5c1a8c709accfa", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9844c7003279d348595689b986a45effcb", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855946bb4ead47bf487d77763c9fc2d8c", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca96b07b5f17aa9c8c67691a1bfaf04c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUMarkerClustering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98835f2b66240c88b07155fce234fd386d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c1806a7bcd750f927e0167f788746502", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98084193e31b24711d2a325239521d9da8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98211a82abb850b53ee692580a7d8c94cb", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f32bba27b0d7d2bd091a9407b2aa71d6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985bc5ff2c496116450b0621a36154f3ed", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878f5a3d29ff61606138dba7cc3a1fd28", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b443f4ec883222dc2cf9035ddf40b27c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ea34deeb335bc13b96d7a13f8055d72", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984328ee610c42f243c8666b5a8f514ac5", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827a7ae95f8e7c7eb509ab80e77427fc1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836b329952af00d673b0ddcf87324077d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98243f757888b2541532e183658fdfa94c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980946e05d92a5e4b9cc3dd7abd12201b1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817e7a885b4c71bd1df11becab1c1cb7d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b1a2fb2c15c412a8a3d4d4d46a3050d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872d7d941e69c71624e1881c5c975ed1c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a2e6d3b828922d776489ef5a5a9be05", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983406c72200dda0307f400ad8f6e48b91", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e539d86a0256bf69929629b718e4e2f0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98582d78674fb71cab9900f4e558b3ea34", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b19096ffe5eb1545eb35b21c609da05b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c4473e1482b4e12e5f097ed2960092a9", "path": "Sources/GoogleMapsUtilsObjC/include/GoogleMapsUtils-Bridging-Header.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9886ff1377319a6fa9c159ee9ce308c400", "path": "Sources/GoogleMapsUtilsObjC/include/GQTBounds.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878d120d57b6a18078a6aa8744998a197", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5980ff3134725c69adf4e4eb0c0df6a", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c1bcdcf2e25599971c2d5de044ade7a3", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec70d0dd225e6c3314bfa7374fcb2630", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819e4730b836d700703e575cbebac3cb8", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837cdffe406e727f87f03b0c648167bc4", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b1a5daee0c3abfecf1275e6cc2767bd4", "path": "Sources/GoogleMapsUtils/Heatmap/HeatmapInterpolationPoints.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bc5e4826ddd8ce93d5f7407154b39f9b", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/LatLngRadians.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980104909dca2e11b2ba78d84f4d93e0e1", "path": "Sources/GoogleMapsUtils/GeometryUtils/MapPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cd0b7d9288f736d61768d4bf7a1542ea", "path": "Sources/GoogleMapsUtils/GeometryUtils/Math.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9825dcdb3713162ab3a6590a40266e86a3", "path": "Sources/GoogleMapsUtils/Helper/MockMapView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f9671d479c737846406fa8b29100b6f4", "path": "Google-Maps-iOS-Utils.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804bef700b8ffa7ccb03ad1da6cc30617", "path": "Google-Maps-iOS-Utils-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f4a5e8ecff66809ab44a566403b98fdd", "path": "Google-Maps-iOS-Utils-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854d77a1da0eec984eb1d00c0f9b991d8", "path": "Google-Maps-iOS-Utils-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dfeb9f7154af66c055813f0efa8db126", "path": "Google-Maps-iOS-Utils-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984c2962c4fbdde476f7a4420701694508", "path": "Google-Maps-iOS-Utils.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98352bc7751e933fbe7bb26364d80d3a04", "path": "Google-Maps-iOS-Utils.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b85263e39bb067f49cfbc6d22fbbee9d", "name": "Support Files", "path": "../Target Support Files/Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986210da4fbceeb58beb07783e335f22d4", "name": "Google-Maps-iOS-Utils", "path": "Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984c55efb1773da40bbf78be005a541ba8", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98044036c4a124845cdda21a38af727925", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e41d9ebd995ca53bbda4c285205c7229", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f54165ec8ef23ceaaf3cd18d982af0b0", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981e3f610c2da24bdc18f553ad57b273e3", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a558e34bc9d6aa72707278233e5de976", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981f06cbfef7908cfd1f04585fa1165037", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817a3ceb1d7bc5dcbb7bd43bff0dd211f", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98198b4daa3c22bcee656c7b8726e1b899", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bce1bd9fe3f5f794e9f46147e2d16257", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98246920040753201a547d93695a24ab7b", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTCompressionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b06268095ff986f34ef5a9bd3441958d", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTCompressionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7a25178aa83ee448a606db603e03968", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTNanopbHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816427d4e7cef47e578048feedcaee6d3", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTNanopbHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb7d29efc070fa22cb88968f93df4d22", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f20f46e165611c3c86ae7f6c843ec84a", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98685df5617e8ef5555fdbcb85e58371c7", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploadOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe8082944469a6e6075070581c00db0e", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploadOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828e2eebe1d798288bd8f1a41a69bcf0b", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a6d88c40fea01a568e7fc464a546c227", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb0032c04f1e185830d48b7569e9e486", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORAssert.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98367a824594359699213d1bc33f19aba0", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORAssert.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a05697a9c9c3ddc410f9012f29b71043", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORClock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826c8c82fc629c10f714aa80d5a5aa429", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORClock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdf01d9aa78c065a37b6a58832c56324", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORConsoleLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f764bc01551fd12b7d6566a8da652ac", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORConsoleLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9844fd7443728e1e694d5bebaa7cd0de0f", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORDirectorySizeTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b5a6cb9047c728ea93fc841eb898d958", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORDirectorySizeTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d53cb4a7a1659ac71c1da7f4b4a28dc5", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREndpoints.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cb611936fe32f6bc79add6b5947fde2b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREndpoints.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988791dd10ffb993fae7bf212aa69c06d6", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREndpoints_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984eddef39ac641a4ca3145d859e08f552", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREvent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ecac34120c3068c9ac8246e837a90d4d", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREvent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983194dff1e349c0e99dda673fe323e317", "path": "GoogleDataTransport/GDTCCTLibrary/Public/GDTCOREvent+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b94c67ba283e831f7c977774747e0fd3", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98036a2c804c1c16d7b3454d780320a5a1", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCOREvent+GDTMetricsSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98099e92e7d1c831080bab528d747ff7fa", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTMetricsSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a621c2db85e43613bb39bd1d0bcc47e3", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREvent_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b13ba4fe8b3015cc8c7fddf8c2af643", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventDataObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980097e27eccd32c5939bcd7ef39f8f75f", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCOREventDropReason.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986074daa49ea5ca180d7f2c0793e4d3c6", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af16e1dc2276e12b545cba1c8e033e60", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8321b09f4146a3558889126ba2eb708", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea5afbbfbf6d3ea35eac76fb6e13dacb", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage+Promises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986224ab8a93e13cfd9d7a7b17176b50b7", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage+Promises.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a515b7863661a66f4ad144b1264ac51a", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORLifecycle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abc44a7cedaa48c60a122e3613426860", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLifecycle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe9a491a4a105681817cdbe4653a58e3", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORLogSourceMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d03091fc179f64c9ae7d8974be4e36f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLogSourceMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ed59fb9e8416870840dda9e99424967", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825638bf34ef0575bc4256f68ad823eb6", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c99ef587fbccb414d985646d7a1799f", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCORMetrics+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd8a00b4ba3dde5032c1053bc56f48d1", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCORMetrics+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983cc8fea392ec27ef00e88260b353222e", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a3e7a30d0410ab2a1b4b19645b67019", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822b061fa59c6686d999292e5de6c0ad6", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORMetricsControllerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de588180243230c936e0e43b8865aa7e", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd9ea68e8fd2dd684460c1a425821894", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7f9efa5b945d063bc7ec58633f8dd0e", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORPlatform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c57d404bb6386350960966548e6b427f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORPlatform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824e45621d1e31f8ac28b2e1058de55d7", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORProductData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985bdd17807f1339bfc5ad2f35884f43f8", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORProductData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801496a4ebf6ce35b117e3907b46eb922", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORReachability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f3e8d1f33309233cf02e30d66c8be02", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORReachability.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8c0c61804108ca6126dd798a99bd7de", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORReachability_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833080c3e952d8a2d15045e7941036a39", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORRegistrar.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a16d0ea6a91f7f298be8ff3d9bc9ff80", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORRegistrar.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895cb8446ae5beddcc24a32d7ea61c895", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORRegistrar_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98972cabe97c758fd0a83221aa466a1285", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageEventSelector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986162652616d61c4e457390076f7cce26", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageEventSelector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98841e33d9e94a75cd9523b9632afa43c2", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORStorageMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f59418fa5181fcd38e3941292452c3c", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985095c3f86163b1f3e2b58ce3989dc312", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c12ffed6ea26bb8aef528be290bde296", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageSizeBytes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b5c737bd77745be7f9b5d58058da23d", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTargets.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821483c877ee402ea68ce5c97d56890e4", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb6215055decfdd42ab598649fb04bd2", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f64fc028f770cf4ffdd15cc9e97cfd4", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98344ba7f7faa5c173b8fc5376db0ab4a5", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTransport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bb194b2803e264eab11d58f46adee7d2", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddf7c96e0b22a4db1f49e0adf9b1a2f0", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransport_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984fbf4f3c8bc71f935762c986ff69cc39", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadBatch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1f71d1fcf44c3248ce0a9d2ed02224d", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadBatch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2a411bd674cf68f3295e49345f27d88", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadCoordinator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dcb4fce3575cdffc510940f7033c1e59", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadCoordinator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c1c030b3fb482d3d390d43fc50d23e84", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878b4e3945448d6bf3158b221df962c04", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GoogleDataTransport.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b118cf9d6045c4e39c79c0ac330c39ce", "path": "GoogleDataTransport/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c82759caa9007be2aa95f61fd14f4882", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cb1623082960cb328f4ee917ace9c0f3", "path": "GoogleDataTransport.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d4fc8e6419bdc85ba897bb7a834397a3", "path": "GoogleDataTransport-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e4cd3603804bcbdc05523a211914665c", "path": "GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896446144ac1bd2c7b40a1d0b995d6c8e", "path": "GoogleDataTransport-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986320bbc8d02dd5b2c9aba63bed7f4a3d", "path": "GoogleDataTransport.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9843a332b7dd2856e3464bfdab733beb9f", "path": "GoogleDataTransport.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988eb511ba5aa7d7351522bcfb1c74a2ec", "path": "ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987e2bd0fbea9c899f35b6dc7630df0d2d", "name": "Support Files", "path": "../Target Support Files/GoogleDataTransport", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fb2600fb92757fe64724baf5be90ead", "name": "GoogleDataTransport", "path": "GoogleDataTransport", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab467c09d12779714e5b5769ef005ef5", "path": "Maps/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e982b722e7e469185643c3dbc753c9ad73f", "path": "Maps/Frameworks/GoogleMaps.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bebbed4cecf2faadad669e393475be84", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.plug-in", "guid": "bfdfe7dc352907fc980b868725387e980c0063e36f1127b18a484f79cd3f0e76", "path": "Maps/Resources/GoogleMapsResources/GoogleMaps.bundle", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e6351b0ffa6723ac5ee40ad3580bd95e", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e8113d856bf2db2588e791c8ea3b421", "name": "Maps", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e981527a0a49657c3c50e0ccf6c99acdffa", "path": "GoogleMaps-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987fe35142225a0aa1ed128f036eae142c", "path": "GoogleMaps.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986d2c45caccdff57dbefb528c78515201", "path": "GoogleMaps.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987069c083775afee1c5a29d633d149571", "path": "ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984321555e1c69a9a70f8e749d32c39fbf", "name": "Support Files", "path": "../Target Support Files/GoogleMaps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be18a37696460f4de4c0e05003b4cb31", "name": "GoogleMaps", "path": "GoogleMaps", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ce1104123078c168960d123176bcb28", "path": "MLKitCore/Sources/MLKit.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986eaa87085a6f7f57355e04e8d88ea359", "name": "MLKitCore", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c4f59fdf0833658a3a8a827de134395a", "path": "GoogleMLKit.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987bcd856bcd2c11ca8301adacdb0d9b15", "path": "GoogleMLKit.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989f659a94ab353372b9b22f07bbaad283", "name": "Support Files", "path": "../Target Support Files/GoogleMLKit", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982774b4f0df90e632ee6a0933af4e848b", "name": "GoogleMLKit", "path": "GoogleMLKit", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aefa510fa35110dda7ee423acbdea0df", "path": "GTMDefines.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983b5ead9d88d8cd0cf6365addda586c0c", "path": "Resources/Base/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986c7c7a279f223f19d91d43cd26c2766e", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98519933bb7432942c19782e398371aa48", "name": "Defines", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f85966b2c8172125ccf23464f0e2cf0", "path": "Foundation/GTMLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aeb24bd30ac1a648d0377a99a18ff2af", "path": "Foundation/GTMLogger.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98cd76c4cd1006d5e15fb8c2a6c1205d30", "path": "Resources/Logger/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d5eb9329e749805de972455780060b8f", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f93133b5adde9ebcc7fbeeb94bce6044", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b53e3a475ac47550b8a2f9cac5266054", "path": "Foundation/GTMNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9822ec146ecc928942d06260fc4742fac8", "path": "Foundation/GTMNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9895970713249a5e7e111b7d384c6c415f", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9839e4e3f3ad58ff897f75030e274bf14d", "path": "GoogleToolboxForMac.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e7984b66e1b1b81e353cd3bceace10f5", "path": "GoogleToolboxForMac-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cbe3ce786f37cc6a02d13e7f8dca3e55", "path": "GoogleToolboxForMac-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f783a211608356de3303c092248592f", "path": "GoogleToolboxForMac-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ef1650fe476bf55f2c33066cf621b37", "path": "GoogleToolboxForMac-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98632e1d6c5359f820b86bfb14c364bc2a", "path": "GoogleToolboxForMac.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d2770e8046b4f0901fdd8d668e7d9f96", "path": "GoogleToolboxForMac.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988356c3185284194ffc11a9e68ae90f7d", "path": "ResourceBundle-GoogleToolboxForMac_Logger_Privacy-GoogleToolboxForMac-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985655497b8ea4b3aabbfa2844455bb7f3", "path": "ResourceBundle-GoogleToolboxForMac_Privacy-GoogleToolboxForMac-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9841eed353d4628e22dbcd8a742cae8f4b", "name": "Support Files", "path": "../Target Support Files/GoogleToolboxForMac", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b7305e06c69f27fc2743971622856c8", "name": "GoogleToolboxForMac", "path": "GoogleToolboxForMac", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98964b9a9349b6f840c2e2d955ed9ddf20", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULAppDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890ed725b8bcdafd6c26f7134a2518916", "path": "GoogleUtilities/AppDelegateSwizzler/GULAppDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aca1ae6e27892c767a547cb18992d487", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULAppDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983425ff44f64306968fd67e1ae4913f80", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897c0c7cb4e6ae0933c13fd82a8098304", "path": "GoogleUtilities/Common/GULLoggerCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8fe6dd5cdf433af63d564309885531e", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULSceneDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98706143f5ffea8d93ef402daa0c7cad4c", "path": "GoogleUtilities/AppDelegateSwizzler/GULSceneDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c159604a0bfca5a2f1e8267862d132c", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULSceneDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984ecbaa3f88bbf4e4c2bb7862680fecf8", "name": "AppDelegateSwizzler", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ae9abd2ae435359a93b2b6a58efddce", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULAppEnvironmentUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f5836c6f486d7618e35c509b1814c62", "path": "GoogleUtilities/Environment/GULAppEnvironmentUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd16946d939e3f8839873ad1d025dede", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982186516ee4ce5e25cfad687904116ee1", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aee1e25d79bea7142f0484e44c85a3cb", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9828302f7903748235ddcb2d6d68a29a20", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c9ebe641006b08857009bc9594cb02a", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a640784ab4add3d93fab368e44dbeff9", "path": "GoogleUtilities/Environment/NetworkInfo/GULNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c085712c3a4fea32ea219c1129c01cf", "path": "third_party/IsAppEncrypted/Public/IsAppEncrypted.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9898673d6c8a776de9d3be83a9c947b631", "path": "third_party/IsAppEncrypted/IsAppEncrypted.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f3b94ac995a47ef53437003f4d076bdf", "name": "Environment", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddd05cb5db5c15844e3feab0c2b07bc3", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98231216202677714fc3617fe62c815341", "path": "GoogleUtilities/Logger/GULLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986fcb30e24ffeb1b97d8f61504fb912cd", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLoggerLevel.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983af495300acfb58daf6ef8b39fa999b8", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db055114466616e84b8006df70c8054f", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULMutableDictionary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b02e6978da4a96e4aed6e64d874317d2", "path": "GoogleUtilities/Network/GULMutableDictionary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d525cda38fe8ee14cde28f428ce42030", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetwork.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d774d0aa89c0a0049d608ad0729a962", "path": "GoogleUtilities/Network/GULNetwork.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0e1e01947b8da9631914236f82a9ee0", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb95ee7463cf81d6c1b5e288df477944", "path": "GoogleUtilities/Network/GULNetworkConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982df9dabd3c1656069fd46b2cfc849c6c", "path": "GoogleUtilities/Network/GULNetworkInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7e2d79f9569db35706bda4c1192f0cf", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkLoggerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872139ddf22e8dc3bae97db989eb6ff56", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkMessageCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98447442989d98ffb77defb78be6af41e8", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkURLSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fde6f00206c7ea695d5fcd65f03a76b6", "path": "GoogleUtilities/Network/GULNetworkURLSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b05ecd822d9ff745d36ebfe2129c6b8a", "name": "Network", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a6c45daabd3e233e4dce3fdcce0f8ec", "path": "GoogleUtilities/NSData+zlib/Public/GoogleUtilities/GULNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98110b495aa3c0518530ff66cf1cd9b614", "path": "GoogleUtilities/NSData+zlib/GULNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98063ae055d7800fb068319c6bc3d227bf", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9857d4a74a6dd132b7d523b86d4ab8ce39", "path": "GoogleUtilities/Privacy/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98af8473dbf21285a8b5f729e35eeefa7d", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98072877c397535c5b38c3c45dea969b3c", "name": "Privacy", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986841f41804df28c594806a2a8de79e2e", "path": "GoogleUtilities/Reachability/Public/GoogleUtilities/GULReachabilityChecker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989af1241e35b8098dc0ed7de761eb966c", "path": "GoogleUtilities/Reachability/GULReachabilityChecker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce6ed583b279aea7c2f4453466c34c39", "path": "GoogleUtilities/Reachability/GULReachabilityChecker+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980248ea7af379aa71d8252de19dad6e20", "path": "GoogleUtilities/Reachability/GULReachabilityMessageCode.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bbc26823ba33ffca7d80dca8aae773db", "name": "Reachability", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d596cc9a2edcdbac74c4d00a5a98b16f", "path": "GoogleUtilities.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a28cc621b3f853d31fa27f6ea62aefe8", "path": "GoogleUtilities-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98eb1ab2922bda32f8f562109bb11e0746", "path": "GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e408d5380963087298bbdd1541b5263d", "path": "GoogleUtilities-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9843505e38067aaf48521b7d9da9914eed", "path": "GoogleUtilities.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e101aa7bf8f26241b71e635c96737324", "path": "GoogleUtilities.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98855c23702dfa13e097e363349646c135", "path": "ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f308e3bb5fba39a3234b8c03ffd778be", "name": "Support Files", "path": "../Target Support Files/GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3ed88c968db59b2e2cc7528d8e67a9a", "path": "GoogleUtilities/UserDefaults/Public/GoogleUtilities/GULUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e89ace186b6e4ae837a2d6be0bc71b3e", "path": "GoogleUtilities/UserDefaults/GULUserDefaults.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b39ec7b5c454bbc0b67574ec99f88a40", "name": "UserDefaults", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837f310b4838bef45af31cdd6a7f3e0e2", "name": "GoogleUtilities", "path": "GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4450207fc12e0b5aa50aeaae8d3459e", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985aa976783d014c89ebdf7f1463927f72", "path": "Sources/Core/GTMSessionFetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a7024b6e477acfd36ad70a89c6e2dcf", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherLogging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9852f9d001b17bba6adbf7350d7fe5df06", "path": "Sources/Core/GTMSessionFetcherLogging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddc3cff98f036c5a612e755c40c11c0d", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807b266f45f55eb0ace033a466122b133", "path": "Sources/Core/GTMSessionFetcherService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b44413a33a133b0998769166f527833f", "path": "Sources/Core/GTMSessionFetcherService+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a101c172230addbd6e099f43de282aa5", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionUploadFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef2302d45f3e102940943725fea60bd7", "path": "Sources/Core/GTMSessionUploadFetcher.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98abad11e7bb9da85d16a78d7375c8b87c", "path": "Sources/Core/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fc1ce1af9a6bada764e6fdb20d055863", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa3ea59fe5e0c22abb3a75a41b36c088", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b1c8e1f2ce4737c6455d0b7e95c0450e", "path": "GTMSessionFetcher.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f9161d9409cb1c452557ce0fa61d954", "path": "GTMSessionFetcher-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98747ba9b253ddb3dbfea26f5c0f010873", "path": "GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830c8b7e635d36a96ff7678f87638b71a", "path": "GTMSessionFetcher-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983e73fa32a8df565e14f0a9a09bb8fb06", "path": "GTMSessionFetcher.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c06eb766128b349a45df767f61545a94", "path": "GTMSessionFetcher.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dcb417b2040084888247fcec6d7e43d7", "path": "ResourceBundle-GTMSessionFetcher_Core_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988af07a9e320f76dace3ce27668064864", "name": "Support Files", "path": "../Target Support Files/GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983088c89413310e3f91c3a13644700382", "name": "GTMSessionFetcher", "path": "GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e984ef15210703c7f272c7b4789cd52b2d9", "path": "Frameworks/MLImage.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98db16b276042474693f49dd11221ab9b5", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9807df0b5acd986df5a6e84527a6b3ab79", "path": "MLImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a23c272d1218e59b3d89e641493aaefd", "path": "MLImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b9ee447672831d1e9e89df710743cf26", "name": "Support Files", "path": "../Target Support Files/MLImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9637e24e2a96072b81cc42c315eb2ac", "name": "MLImage", "path": "MLImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e982dce29cf64059d0b6a5fd62a094a0682", "path": "Frameworks/MLKitBarcodeScanning.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9883ab7e28c1ee1bd202c4d32c288d0894", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983b0ff734f3d761c8239ad2c5fa4471f0", "path": "MLKitBarcodeScanning.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986cb03a301f2669f7f347c2817acb8e95", "path": "MLKitBarcodeScanning.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b0b7a4c85935d7fea11b5c38a8d5faae", "name": "Support Files", "path": "../Target Support Files/MLKitBarcodeScanning", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0c45bef9c7bbc9a22aedc9e62ba0309", "name": "MLKitBarcodeScanning", "path": "MLKitBarcodeScanning", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e983fcdf5896272beee738db1a537d959db", "path": "Frameworks/MLKitCommon.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802fbe0456f510a87ddaa526d5d430d34", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98870136481ff479dfc0cb3f2880d011de", "path": "MLKitCommon.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98db3ca82cb3e7e1e4ab201b8748088a92", "path": "MLKitCommon.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b5092d650cd894ed14872e70fa31dce0", "name": "Support Files", "path": "../Target Support Files/MLKitCommon", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c59e8b7e75b74760504fe0798abcbc00", "name": "MLKitCommon", "path": "MLKitCommon", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e982cf46017a4240adc0967783e381c727a", "path": "Frameworks/MLKitVision.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f98677af2778675b595ac583c5a5cd", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985569a71a549f28f689020313db83dc31", "path": "MLKitVision.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989df47b0560909781ed3ec478bfa9411d", "path": "MLKitVision.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984bcc5ddac4f02d34177e4eeacbdc71d2", "name": "Support Files", "path": "../Target Support Files/MLKitVision", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b5e2fda50006aaec07a296da54820fd", "name": "MLKitVision", "path": "MLKitVision", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989512645093a8cd234009a9fb16594eb7", "path": "pb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b47b9d20114761c4b9240487a2293825", "path": "pb_common.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff1888a2758d0063e26282b4cf4acc3b", "path": "pb_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9837dc84d3fca2f842ab084e67d39030d4", "path": "pb_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd67b9a8825bdfef79346da339ad36e2", "path": "pb_decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9861b6171d2d8eb93158011cde9335083d", "path": "pb_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989fae2e40d9775d950634794ff02a3dc6", "path": "pb_encode.h", "sourceTree": "<group>", "type": "file"}, {"guid": "bfdfe7dc352907fc980b868725387e9882329b84df99ac5f96c28bb55cd2b607", "name": "decode", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98033858fcd82aa4b090b2adbf41f45602", "name": "encode", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9835a1238e4e9a2062e770969f4b804bee", "path": "spm_resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980686f5405a2a3d0fe2aeba3e8a6e9bfd", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986eb872c5dab779fe89cca683fba0c3c8", "path": "nanopb.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c7a07ca171552c1c7dbbc1143b2c4be", "path": "nanopb-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985ddde06f25affbc5e3f30d883b006a23", "path": "nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9706319bca908ee0fbb98050b3206ef", "path": "nanopb-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851111e1a993143b34bc244b95b8764bc", "path": "nanopb-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985e4c3e1d1045b1eb16447b0aa4f62369", "path": "nanopb.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9873da9a151a32b93b09e014c757389a97", "path": "nanopb.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cac5588d2cb1581eb417b1c2cb6890ae", "path": "ResourceBundle-nanopb_Privacy-nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a3da5aa8caca18902cd0378e3ed4986f", "name": "Support Files", "path": "../Target Support Files/nanopb", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98938ae0094581a0125deec82b37fcad87", "name": "nanopb", "path": "nanopb", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8ac58af51e9296f254e4deac9a6332d", "path": "Sources/FBLPromises/include/FBLPromise.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984815821345749f68659171496e4cd9c6", "path": "Sources/FBLPromises/FBLPromise.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7f40a72ea937aeaf20e83dac9ca39d1", "path": "Sources/FBLPromises/include/FBLPromise+All.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98040ffa5573a6bd0cd5cfd4af8863193c", "path": "Sources/FBLPromises/FBLPromise+All.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986066359470d33b4c913375c506bd0586", "path": "Sources/FBLPromises/include/FBLPromise+Always.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c69eecd79d44fdd1a64239a96fdc84e8", "path": "Sources/FBLPromises/FBLPromise+Always.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98996652529d4e35ef7382c631f1081693", "path": "Sources/FBLPromises/include/FBLPromise+Any.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e5a87603428fae90f8afe9e4f56e0af3", "path": "Sources/FBLPromises/FBLPromise+Any.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d100f3d94a6bee88c3a85d225c7e5fa9", "path": "Sources/FBLPromises/include/FBLPromise+Async.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875c38348923e37e3d157eb2803c4cf76", "path": "Sources/FBLPromises/FBLPromise+Async.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5f34964863b8bc8c8790aa0d238490a", "path": "Sources/FBLPromises/include/FBLPromise+Await.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce3f34fe3108ed4f00260c92455f57b6", "path": "Sources/FBLPromises/FBLPromise+Await.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b26b82f6132757f7ac40473e0aa5ba5a", "path": "Sources/FBLPromises/include/FBLPromise+Catch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b03c3f0959d335e519fc792467a83ae4", "path": "Sources/FBLPromises/FBLPromise+Catch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a6880d7aeb857c1ea9046c308427600", "path": "Sources/FBLPromises/include/FBLPromise+Delay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986d832cda6af51aa1e2092abcf13f27f7", "path": "Sources/FBLPromises/FBLPromise+Delay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b040af088d2e5292d5d104d6b8b2b18", "path": "Sources/FBLPromises/include/FBLPromise+Do.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b94edb4af824870b15c4061e76e4a07", "path": "Sources/FBLPromises/FBLPromise+Do.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a706dc752de14e9830e0802116bfb26c", "path": "Sources/FBLPromises/include/FBLPromise+Race.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9886b1f61052055847698fefbef8f19993", "path": "Sources/FBLPromises/FBLPromise+Race.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981747bf6277d31adbd1e81d1a2f948b3a", "path": "Sources/FBLPromises/include/FBLPromise+Recover.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b8daaabda770f0cc7e817bd386febd90", "path": "Sources/FBLPromises/FBLPromise+Recover.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98374fd14452808ed90ccbeb33bb66f984", "path": "Sources/FBLPromises/include/FBLPromise+Reduce.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4cc7dfb434bf5720b7bccddfeec6514", "path": "Sources/FBLPromises/FBLPromise+Reduce.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c606755a20b57fb780d9ef3412464b9", "path": "Sources/FBLPromises/include/FBLPromise+Retry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae73c38083ef406b68b6cdd426369f96", "path": "Sources/FBLPromises/FBLPromise+Retry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af88911d72dc2cecd0cdde47628af625", "path": "Sources/FBLPromises/include/FBLPromise+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844c3aefae2b9a4417440f67639695df6", "path": "Sources/FBLPromises/FBLPromise+Testing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842e05bd23e86424a4076d29b215e1181", "path": "Sources/FBLPromises/include/FBLPromise+Then.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d034dee23876b09451b4f6457f5499a", "path": "Sources/FBLPromises/FBLPromise+Then.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8c6f7f9d7a95fae921b2b3ca59394a1", "path": "Sources/FBLPromises/include/FBLPromise+Timeout.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98161b2d9b33aeddccde02b6279859ace1", "path": "Sources/FBLPromises/FBLPromise+Timeout.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9873ec53c290154e33d064f866b25b8cb0", "path": "Sources/FBLPromises/include/FBLPromise+Validate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b997faebb37a3dbfec73149252b06f1", "path": "Sources/FBLPromises/FBLPromise+Validate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8429db79ac6a327e07a368a4a3a0f9d", "path": "Sources/FBLPromises/include/FBLPromise+Wrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827969122ecb121246400284b77aac2b2", "path": "Sources/FBLPromises/FBLPromise+Wrap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986012d73eb986e5a1aaa7952a9caeae59", "path": "Sources/FBLPromises/include/FBLPromiseError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987117a696df478216ae92c82940acb1f2", "path": "Sources/FBLPromises/FBLPromiseError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9861a2f55ed8c6ada045fa8f0d5d31b712", "path": "Sources/FBLPromises/include/FBLPromisePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b35f2140c1908280a190e037d164935", "path": "Sources/FBLPromises/include/FBLPromises.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982c65ba8dab10786b486ab73b7a946be9", "path": "Sources/FBLPromises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d836056fabec7b656ffe960c65cfb726", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b933bb239ff54b10e09ab5c0d094506c", "path": "PromisesObjC.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a22e2851a0f20411ac6fa32ecfe3365", "path": "PromisesObjC-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989a768e7c9f78a76d2b3708b22952db06", "path": "PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9a0bfafe21945a4d8dffe525e1cc2ac", "path": "PromisesObjC-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9845b6c9f2ed7334efbe7153e6fa189da0", "path": "PromisesObjC.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986073180e690f94e4bda9fa6ec25f4d20", "path": "PromisesObjC.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d6cbbf3aa279d46f9b7ea677b45a8a7c", "path": "ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c95c88ac3a360d1305d21e72b816f545", "name": "Support Files", "path": "../Target Support Files/PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980abafcb8bc25077155f129168ef019d3", "name": "PromisesObjC", "path": "PromisesObjC", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852927b991ba97e297d06c590267bff8c", "path": "objectivec/google/protobuf/Any.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef8124d1995320584e0fc200ed088d28", "path": "objectivec/google/protobuf/Api.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885f6127d8ad5bb6e0618734e89296f6c", "path": "objectivec/google/protobuf/Duration.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b65e8f80a2e979ca87d95c6ae1238370", "path": "objectivec/google/protobuf/Empty.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98abed9e8a5ede48b5a5868f21e46f2671", "path": "objectivec/google/protobuf/FieldMask.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe899b7fb57b62a8579435642eb5070c", "path": "objectivec/GPBAny.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877e97c6f70b305e5b8f3ecad267fd066", "path": "objectivec/GPBAny.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c20ca9e95eba8b07c0d16626c0c842fc", "path": "objectivec/GPBApi.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984adb4e56ec86728869719c3c77fe805b", "path": "objectivec/GPBApi.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b96fa4ceab57091f4a684cbd7bba208e", "path": "objectivec/GPBArray.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c8167ce037738cd382354ea87cd7ce2", "path": "objectivec/GPBArray.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983074efdf26f2b8145244fde9b3aca24e", "path": "objectivec/GPBArray_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5710e1978a8cbe13fcbf6bff9847a80", "path": "objectivec/GPBBootstrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d35c4cd72d2e80f2a20b7119a89ba98", "path": "objectivec/GPBCodedInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac08bec79a29541d92f64e22f6fa5fd1", "path": "objectivec/GPBCodedInputStream.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858395e2664e1a902449abce349127dce", "path": "objectivec/GPBCodedInputStream_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f4e0f9f16d829b0d324698dfdb4f6b2", "path": "objectivec/GPBCodedOutputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804da7557d522193ae1d38675e2add64a", "path": "objectivec/GPBCodedOutputStream.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b547bdd0b8905ead3da1294f1df67ae", "path": "objectivec/GPBCodedOutputStream_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf436ac50bd917c4e0fc217ba26a28f0", "path": "objectivec/GPBDescriptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988607361981e2acae4da979915de98310", "path": "objectivec/GPBDescriptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d145d261aba90e1101d0eaea2fb0d13a", "path": "objectivec/GPBDescriptor_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98070e8f7bbe1f861223c69805dd2b5bc3", "path": "objectivec/GPBDictionary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987219696a64d9f8e544ef1962a6bbac36", "path": "objectivec/GPBDictionary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f660d296d5e2a63fd87ec44c10e55f58", "path": "objectivec/GPBDictionary_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cbd426b49b444014b73e9c2bbfd2738d", "path": "objectivec/GPBDuration.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985843e9fa4eed0c44e6bbcd27805d8751", "path": "objectivec/GPBDuration.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dab761090697d617d7b3df6c5e36e4df", "path": "objectivec/GPBEmpty.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af9f7aaff1b41e5378b9da344efc6407", "path": "objectivec/GPBEmpty.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5792a55df9bf8eb1bcc68b93e4e65b4", "path": "objectivec/GPBExtensionInternals.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f28e9bd5a8aa3c82090054db3b2e9965", "path": "objectivec/GPBExtensionInternals.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897d9e41f810daa0c3a39d0cad880d800", "path": "objectivec/GPBExtensionRegistry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c789bd8476e1ef357f4ff1d40eef7573", "path": "objectivec/GPBExtensionRegistry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894942b693fae59b8ffe5b6243a556bfc", "path": "objectivec/GPBFieldMask.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc3b4edf724379d8a3f13abff9c0d544", "path": "objectivec/GPBFieldMask.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984125de4e48b4b20989acb3de2b017782", "path": "objectivec/GPBMessage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef4243046fdf8b3d8609c297a8a6f8be", "path": "objectivec/GPBMessage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8a21a4d3030468673634d682dea3921", "path": "objectivec/GPBMessage_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806600300f1bf91ec10d18efdddd40bab", "path": "objectivec/GPBProtocolBuffers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc6c08c34c0bec772fc8b9573e78bb8a", "path": "objectivec/GPBProtocolBuffers_RuntimeSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9c9222a9c85a58a8d725fd6c2f36a84", "path": "objectivec/GPBRootObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf318425016d70957b881715b46e65b0", "path": "objectivec/GPBRootObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e0bce84052db193f5a227a9dc4d1af0", "path": "objectivec/GPBRootObject_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984bddef9e3973b3fbf9f4d201bd49be86", "path": "objectivec/GPBRuntimeTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a54a22baae7f07db9de76faf13cbeb87", "path": "objectivec/GPBSourceContext.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b551f1388e2a954867e9e55d7418e92", "path": "objectivec/GPBSourceContext.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980068676c554405b72fb1f9f1407d0986", "path": "objectivec/GPBStruct.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980797388b78fce494030ccacf6259c29a", "path": "objectivec/GPBStruct.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98164a5d88bd841cbe6f627d775012329e", "path": "objectivec/GPBTimestamp.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0caff6e1524170958abc28ac768fd6b", "path": "objectivec/GPBTimestamp.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989493cb44605032c1d7beec3b35df6ff7", "path": "objectivec/GPBType.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bce73ff07962d6d73bdb4c153cfa36db", "path": "objectivec/GPBType.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cfe2f11637526409953a146e76986b5c", "path": "objectivec/GPBUnknownField.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988228414a8616d4a02e57b01882dffa87", "path": "objectivec/GPBUnknownField.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983dccb7b9b85c0143cb89b425cf105e23", "path": "objectivec/GPBUnknownField+Additions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98562e1842e88c8b789ec8550d8a8002d9", "path": "objectivec/GPBUnknownField_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e29fc0bb25098b707571ae8c8d2c7328", "path": "objectivec/GPBUnknownFields.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9808de8d96a8768ab9226cbe3b1ccb938b", "path": "objectivec/GPBUnknownFields.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff0f8edc6c7120bc22aa4c7e06bbc1f4", "path": "objectivec/GPBUnknownFields+Additions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987153aa27c4426badf7339945c6969f02", "path": "objectivec/GPBUnknownFields_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa774e6fb123807fc2e65b246aefbef4", "path": "objectivec/GPBUnknownFieldSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c04df2874b961ab5e215773e6aad2c3c", "path": "objectivec/GPBUnknownFieldSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd14f6427e9f279f7c8468e10138d64e", "path": "objectivec/GPBUnknownFieldSet_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7c86b023b1a70969b5980eea8355e90", "path": "objectivec/GPBUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b6ce32dbc93ec3e54c283ca8a7535dd", "path": "objectivec/GPBUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3ead6606dceace1fefad5f00a5d6d2b", "path": "objectivec/GPBUtilities_PackagePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98146e62e331774186b94252bfe80a9bac", "path": "objectivec/GPBWellKnownTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f94002e7317d4872878ca282cac8002", "path": "objectivec/GPBWellKnownTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb22c84870cd8e47096ce6b3301dbed1", "path": "objectivec/GPBWireFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9839254be459d8a3f672f549caa600c804", "path": "objectivec/GPBWireFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ad33f8770219ce7272c2e4f17b2f8e1", "path": "objectivec/GPBWrappers.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d16eaf6e3c003eee01cc8bfde348b96b", "path": "objectivec/GPBWrappers.pbobjc.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865f764d821e01f78da17235b1258013c", "path": "objectivec/google/protobuf/SourceContext.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f9f84c99d6c4d301fa5059a3318776a", "path": "objectivec/google/protobuf/Struct.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805d7589ea71fb3dbfa7aa371a85516c3", "path": "objectivec/google/protobuf/Timestamp.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a17365f9ffbf97e609c7e7192a98de21", "path": "objectivec/google/protobuf/Type.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8aafb327992073e048f2f8790162184", "path": "objectivec/google/protobuf/Wrappers.pbobjc.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f5d533f4132c10e30ea0924477944d08", "path": "PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ef70654c149018e4d663f299518ac577", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e837e511c44c0cae59afb936e63ffbee", "path": "Protobuf.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e00cb09ac0a65fc3d2b74692c174d235", "path": "Protobuf-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981a27548816d05f951899be2854e4a3ce", "path": "Protobuf-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a600d408ac0f203fbccdfb33312fbfa3", "path": "Protobuf-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98177deb075218a7a833ebf3f6d2a1f9c2", "path": "Protobuf-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989041ecfee93a2ee3cc0c9f7ae7c08ee4", "path": "Protobuf.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98503f323808186a4f8162d6748381de51", "path": "Protobuf.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983d308696000098e4959041b08e1eb175", "path": "ResourceBundle-Protobuf_Privacy-Protobuf-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982d0e80d604561238ada0722a29bf76e1", "name": "Support Files", "path": "../Target Support Files/Protobuf", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884aff0ef4154c62cbbf7d16541ef435c", "name": "Protobuf", "path": "Protobuf", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98304ec752d49654371095b0c5b803540b", "path": "Sources/SwiftProtobuf/any.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0cc1b26402e4c2edbcc3c28e0393206", "path": "Sources/SwiftProtobuf/AnyMessageStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98abb6182278f2e5180ac60e85d0ec7a7c", "path": "Sources/SwiftProtobuf/AnyUnpackError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b99f7d0955263103e4d734c3854cae15", "path": "Sources/SwiftProtobuf/api.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9825d6f2623644cba96a5d1cb95f7ccb71", "path": "Sources/SwiftProtobuf/AsyncMessageSequence.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b27ddc78786e9fe9494e0abc8543cd86", "path": "Sources/SwiftProtobuf/BinaryDecoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9837f506efb8682827e56621ed92c53ba4", "path": "Sources/SwiftProtobuf/BinaryDecodingError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a43bb12aa3fa260d3106d4311d789b3f", "path": "Sources/SwiftProtobuf/BinaryDecodingOptions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e4c48eba8cb2c6968152d125ad2a8a51", "path": "Sources/SwiftProtobuf/BinaryDelimited.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989db88fdc3ca947d324045d5f97945827", "path": "Sources/SwiftProtobuf/BinaryEncoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986c0919588ae715b27a4c37cce801ab90", "path": "Sources/SwiftProtobuf/BinaryEncodingError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dd6c7612a98ffa542a15e3f73805623d", "path": "Sources/SwiftProtobuf/BinaryEncodingOptions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ae242998da9c65aec4f4d06de9c38d90", "path": "Sources/SwiftProtobuf/BinaryEncodingSizeVisitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cc39dfc6483da5ca20b999f3fc0a9d5b", "path": "Sources/SwiftProtobuf/BinaryEncodingVisitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bbf49680de00709f3187782c872c3be5", "path": "Sources/SwiftProtobuf/CustomJSONCodable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983a609087a86c920e8d266e757d2d164d", "path": "Sources/SwiftProtobuf/Decoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98087639d33c58e9363360c9b74fb9703a", "path": "Sources/SwiftProtobuf/descriptor.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da852213d3f5eea89071d69dd6025c7f", "path": "Sources/SwiftProtobuf/DoubleParser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b0559c00fa87d52f5eed91b5f662949f", "path": "Sources/SwiftProtobuf/duration.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984eb512d60dc929a3d2fd2ca8881eddfc", "path": "Sources/SwiftProtobuf/empty.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988f4eea23aae6c7e931f22b00558ff74c", "path": "Sources/SwiftProtobuf/Enum.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982847de537b9e68b499e07111b0932745", "path": "Sources/SwiftProtobuf/ExtensibleMessage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987105209dd3b90d066d4bcee66dd9f5c2", "path": "Sources/SwiftProtobuf/ExtensionFields.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ab03e61cac818b465360bb16bf00c64c", "path": "Sources/SwiftProtobuf/ExtensionFieldValueSet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98427eb4c64d37aed50e57619ba5644d55", "path": "Sources/SwiftProtobuf/ExtensionMap.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982a955ae1f212595662f62b02e3ad6cd2", "path": "Sources/SwiftProtobuf/field_mask.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f1725929a89b9bdba885b99fa6540925", "path": "Sources/SwiftProtobuf/FieldTag.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982f2113eb22cccaf1903ddd732915ea8e", "path": "Sources/SwiftProtobuf/FieldTypes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982bf39f0d5842f1cdb55efc27c62e4792", "path": "Sources/SwiftProtobuf/Google_Protobuf_Any+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f14e1cecfc1c045962e1f10982d426c4", "path": "Sources/SwiftProtobuf/Google_Protobuf_Any+Registry.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ba53c8b7c971a8f8b8a2131ff09687c", "path": "Sources/SwiftProtobuf/Google_Protobuf_Duration+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e25871f129245b477013c23ef6e3580e", "path": "Sources/SwiftProtobuf/Google_Protobuf_FieldMask+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bae1d2a0ef8f8c9fe1d5e85812352253", "path": "Sources/SwiftProtobuf/Google_Protobuf_ListValue+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e6c679ebd65d471ecb61f7c8f9236ec7", "path": "Sources/SwiftProtobuf/Google_Protobuf_NullValue+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f6fedeb9317bf14a98ddba0d3acfae9", "path": "Sources/SwiftProtobuf/Google_Protobuf_Struct+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a67c1a7bd2abaa12bd2507a0d742b35b", "path": "Sources/SwiftProtobuf/Google_Protobuf_Timestamp+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980fd6bc1c1ee1af8bf0ee32e71806f269", "path": "Sources/SwiftProtobuf/Google_Protobuf_Value+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989a5ce09fe6daa416a58646b413a1fd80", "path": "Sources/SwiftProtobuf/Google_Protobuf_Wrappers+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884185c24389e96fb45a73083497b24a6", "path": "Sources/SwiftProtobuf/HashVisitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98531881443a94f9e469ac51e21ee666fb", "path": "Sources/SwiftProtobuf/Internal.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9802c0d5a9cae926fd17c7379672372357", "path": "Sources/SwiftProtobuf/JSONDecoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d0b694e1e998f24566e61ad204cd9a9", "path": "Sources/SwiftProtobuf/JSONDecodingError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e423862f7e0d3c907f49afe997c51647", "path": "Sources/SwiftProtobuf/JSONDecodingOptions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c69d5546a43d6ff9cf6174b47e10ee10", "path": "Sources/SwiftProtobuf/JSONEncoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cc5797be517c603775278ada81201d81", "path": "Sources/SwiftProtobuf/JSONEncodingError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f89728e4cf140c015e1b66e74b35048", "path": "Sources/SwiftProtobuf/JSONEncodingOptions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c6322aa9831d57f8ba60f4c3da74803", "path": "Sources/SwiftProtobuf/JSONEncodingVisitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d7b06dc0241db1e0e8e9748527ccad2f", "path": "Sources/SwiftProtobuf/JSONMapEncodingVisitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988072bedaf5c3a9eff6c432777efacb25", "path": "Sources/SwiftProtobuf/JSONScanner.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cf62b8b4fb196d3273f8188e3faa5de0", "path": "Sources/SwiftProtobuf/MathUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9872893a6201212c1617bae4a833463dc5", "path": "Sources/SwiftProtobuf/Message.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982f036f25137af68c689f3106d8b5be17", "path": "Sources/SwiftProtobuf/Message+AnyAdditions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986059cdd1641704249d158051e1b41f4e", "path": "Sources/SwiftProtobuf/Message+BinaryAdditions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa133c3e31f1f98275afb4b90c671f32", "path": "Sources/SwiftProtobuf/Message+BinaryAdditions_Data.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de62c359c264df34fe0fb74168d2b291", "path": "Sources/SwiftProtobuf/Message+FieldMask.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98935041e48375ee11e88ccc7c320f6e65", "path": "Sources/SwiftProtobuf/Message+JSONAdditions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ba47a4a64129d5aea8a02740dafe0c48", "path": "Sources/SwiftProtobuf/Message+JSONAdditions_Data.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9828821e70c88ecfd07ef1cb54e1342c1d", "path": "Sources/SwiftProtobuf/Message+JSONArrayAdditions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f6fd5703fc71becacf686e2067164a91", "path": "Sources/SwiftProtobuf/Message+JSONArrayAdditions_Data.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9808c4edaf9bd87bdf358a0112d867ccaa", "path": "Sources/SwiftProtobuf/Message+TextFormatAdditions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981181f5b252f833276a40bf603beffb7b", "path": "Sources/SwiftProtobuf/MessageExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983567b66acffbed37919c02eebe8073af", "path": "Sources/SwiftProtobuf/NameMap.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9874f4a7b8daf0e496e8afea6fe30af3cc", "path": "Sources/SwiftProtobuf/PathDecoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980630ae0e54737a8ba14ac960a36f9328", "path": "Sources/SwiftProtobuf/PathVisitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cf6d91a6fe075220d3aaf0172ece1d0f", "path": "Sources/SwiftProtobuf/ProtobufAPIVersionCheck.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9a9eb17d2926ae86c2b69eb7ff55607", "path": "Sources/SwiftProtobuf/ProtobufMap.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9859a0a84689c804d5c28b0af0f3d641f9", "path": "Sources/SwiftProtobuf/ProtoNameProviding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c11efddafdcafae72c6046fc72827467", "path": "Sources/SwiftProtobuf/SelectiveVisitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987e05cf4d24db83aff48dc2cbf7080810", "path": "Sources/SwiftProtobuf/SimpleExtensionMap.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985dbb16a40de5387b929c9531b654add5", "path": "Sources/SwiftProtobuf/source_context.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983506e9755d351768dcc9a8921c0a4b15", "path": "Sources/SwiftProtobuf/StringUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98414f452c1e5ec4c3aec4adcbd88241cf", "path": "Sources/SwiftProtobuf/struct.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c46599e171fc9469491427ee72cba85", "path": "Sources/SwiftProtobuf/SwiftProtobufContiguousBytes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c5137556e2d227f21cb4de0391425a8f", "path": "Sources/SwiftProtobuf/SwiftProtobufError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860590ed08412bb8f753db7bb4f2da958", "path": "Sources/SwiftProtobuf/TextFormatDecoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988dc507850221fad548f72c3c7c4d82bc", "path": "Sources/SwiftProtobuf/TextFormatDecodingError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d2be085faac65a0a0a6aa525b0f9b57e", "path": "Sources/SwiftProtobuf/TextFormatDecodingOptions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984c58daf7df83a88f2b046ab57b75939e", "path": "Sources/SwiftProtobuf/TextFormatEncoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b00045b6337019d8f25b815ba0c40c3c", "path": "Sources/SwiftProtobuf/TextFormatEncodingOptions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e80af4d61d9159ace61a601df6671363", "path": "Sources/SwiftProtobuf/TextFormatEncodingVisitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870ddca4ba40145ebd7b6e55469244cfc", "path": "Sources/SwiftProtobuf/TextFormatScanner.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98901779afafab6428eeb6431f3c086a6e", "path": "Sources/SwiftProtobuf/timestamp.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9844fd277d2de6bb9096a7c0f3a1d3a4b2", "path": "Sources/SwiftProtobuf/TimeUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980bde396c5d2aa1ba21585f3068e76984", "path": "Sources/SwiftProtobuf/type.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985b95cc4d23dc0396a89e5a2e625e0638", "path": "Sources/SwiftProtobuf/UnknownStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985907611541118a0fc00ad23c533dafb6", "path": "Sources/SwiftProtobuf/UnsafeRawPointer+Shims.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98721c1ccbbbe4dbb0fb6c36a7e5e6acb7", "path": "Sources/SwiftProtobuf/Varint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983892f70c334fe598d761ce0928437b91", "path": "Sources/SwiftProtobuf/Version.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a782b3b32f3489c225227fa37cab2d9d", "path": "Sources/SwiftProtobuf/Visitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98492a0d28afba6ac3b22c9d7f9adbfafd", "path": "Sources/SwiftProtobuf/WireFormat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987dbe05b452db8a41a3c897f956945e84", "path": "Sources/SwiftProtobuf/wrappers.pb.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9896f6ef4fe8989e727ad7d96131b6720b", "path": "Sources/SwiftProtobuf/ZigZag.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9844a247c419a5b427ade65a57e7a8dc63", "path": "Sources/SwiftProtobuf/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a819f51ad9a71698435f4f156907d5af", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982272ec09e61ace47e8f240949cdd9ee7", "path": "ResourceBundle-SwiftProtobuf-SwiftProtobuf-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f3a3d1b4bc707bed3eefca7e8166fe7c", "path": "SwiftProtobuf.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986e7c68114d33536dbc80e6138ffae7c7", "path": "SwiftProtobuf-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983c9b5b34d31e80753bb1ee09da656d7d", "path": "SwiftProtobuf-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e237f699047e4d7475a3ec006b7edb4b", "path": "SwiftProtobuf-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c5dc6d54a6d41fa2037123b4fbcf3db", "path": "SwiftProtobuf-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98167e601826d191fe6969affa6116528d", "path": "SwiftProtobuf.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98330fdf32c4ccef1f9c41b33e8e5fdc69", "path": "SwiftProtobuf.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a3a7cb0b669d13ece3f7b28cb9748ee3", "name": "Support Files", "path": "../Target Support Files/SwiftProtobuf", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f704dfa36b2b9e7f7eb7eca4340697f1", "name": "SwiftProtobuf", "path": "SwiftProtobuf", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821de19d81b2de83409e4273b39e6282b", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98326ac3a784b0490f793cfa6c514c729c", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98bc9b33687cf974a825db433d5a4ce66f", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987edd064bed57826d8d017ae149a3d52e", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987fb378bbcd6c5ed58c790a44f8de1ad8", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d314b0e0b0622c4b6805a741f2686226", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98c868b1c7c8b6f1d4f98ebdaeb296a675", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986fd78883b66d5d9077a96975628773ce", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e983a42af4f91479a2ff6a94702f9452725", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847c36cf1fe2165ccb62332bdeff26348", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985f6ec3891a0be6525111807007bbcf76", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988db807c74690161e03dc575ee7464945", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98778a51cd43710d278531fd4c4e5ed80f", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9863ac39dbb3c5e6697e1e483c96fdcf12", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d87df373aa945c7cffc56572b5b5c1d0", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/somecode/aslaa/web/aslaaios/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/somecode/aslaa/web/aslaaios/ios/Pods", "targets": ["TARGET@v11_hash=ab5629dfab9a871a9086c9a3315769f7", "TARGET@v11_hash=57eaf4dc8bc992540d110ac90960e143", "TARGET@v11_hash=04eb1944b27961219a7bb353fdcbcf04", "TARGET@v11_hash=408a1577d70d9b917db9edcceba66645", "TARGET@v11_hash=642a976f66830bb15065ed817c6012c4", "TARGET@v11_hash=b8b1258f4a775db640b021d6e8ab1ae4", "TARGET@v11_hash=7960730db14f479430de50c086513141", "TARGET@v11_hash=7db8a911b79918bbb432239d1b134e30", "TARGET@v11_hash=d3834b2744983c46c8e0ab8e243b1a30", "TARGET@v11_hash=88636754134bc3fe072f8226e4d5017c", "TARGET@v11_hash=7ce1956e899419d3aba6fc463416cdbb", "TARGET@v11_hash=9a147f8dd48607d0a7278d12ce95eda9", "TARGET@v11_hash=d03da7f038919ff7dbd03c9591c83de1", "TARGET@v11_hash=e191d1294cb8eedc55f26e3c0d2b8a5a", "TARGET@v11_hash=4cf378015a0054ecdb9e5ff5e8dba993", "TARGET@v11_hash=7a37b45f36d83f839d7247e495853341", "TARGET@v11_hash=07977d8165d38ec4da8501809e56fbf6", "TARGET@v11_hash=c90ed7462434c72d66fd5bb4cf763e57", "TARGET@v11_hash=d930cc5c92e27c45bde2a2629ceef0ac", "TARGET@v11_hash=c7d71affb27bfe8836c4b72b136aaca3", "TARGET@v11_hash=c689c007e50dcd0c573942610e5805e5", "TARGET@v11_hash=5e4fefd171879127f88ab6fe0eb4806e", "TARGET@v11_hash=1d705c822fafebce480dd22b96fdb438", "TARGET@v11_hash=a14c26633ba308ae74f48c7915bbcc36", "TARGET@v11_hash=7c31fcb55046c89c6c9289fce7a2ede5", "TARGET@v11_hash=22e7733d3d06333912ae87a5eb09266f", "TARGET@v11_hash=8b570e8cd01da8cd0f79ee2622ac6c7a", "TARGET@v11_hash=96247b3de56b0c840ed4b6547da328ce", "TARGET@v11_hash=e210f99da35d730ce2369a574a9c7cbf", "TARGET@v11_hash=e0a14ef33293ea66d8c192f04391c16e", "TARGET@v11_hash=bc0ec40c18e316c37e0d966e10e58016", "TARGET@v11_hash=479e5055eac5694ac5a26ef662ba8b1c", "TARGET@v11_hash=b1ba5e81ca55e4192d358695aa503f22", "TARGET@v11_hash=8994b71eb2a9b438eb70de6f297dfedf", "TARGET@v11_hash=12a4c269a202599af2256c3f8c52a6d9", "TARGET@v11_hash=11f3b3f32046209a5b695d528df59044", "TARGET@v11_hash=514f194c21550d2d66f6bc5eb6c43d50", "TARGET@v11_hash=3d50963a503eb05210e7802b6aafe39e", "TARGET@v11_hash=b63d8f2ef518bfe68c0404c61cf8ceb5", "TARGET@v11_hash=bc81acd84eeaf6e4843d9e6b67ea705a", "TARGET@v11_hash=d3a09059400492e97d3c8c66b930874f", "TARGET@v11_hash=56cb3a14ca8c08f4a8f40c28e318d14d", "TARGET@v11_hash=f0c8bf3e999d051e0abf03201ac31f4a", "TARGET@v11_hash=08b22c8ea47dd9fc3899ebccfa3460b5", "TARGET@v11_hash=7bc06086a7060b9f181edc223bd7dd89", "TARGET@v11_hash=8f8fa37487b94bf92c501740d728aa4c", "TARGET@v11_hash=217bae2cfcc48aed0181020aa1bd6a62", "TARGET@v11_hash=54c44fb4f8a16fa3ac7c32e904689ba5", "TARGET@v11_hash=6f0aa993ab4ef3d40451f5d2ce52287a", "TARGET@v11_hash=7f2c561d7d67cb8e7042927de2cd28d4", "TARGET@v11_hash=c82fb99f37f6a6e809823257e32bb16e", "TARGET@v11_hash=58bf04becc26c8163845f516ceed21a6", "TARGET@v11_hash=1a93713c764c31fd576bb9eee1be7dc4", "TARGET@v11_hash=ae095d34bd49edebf71c934e50f5b1e5", "TARGET@v11_hash=000fc4762ca4c5fe991bd92f1cf52702", "TARGET@v11_hash=972568be11d64f3f89adae7d8c7cf1dc", "TARGET@v11_hash=6999089519b5c6ff09907b22ff7086c0", "TARGET@v11_hash=6435347eb25ecd74025ec330c7803614", "TARGET@v11_hash=cd660c2436506a972bd52d7a35c8ee96", "TARGET@v11_hash=fef126af4579efd3645d6c617ffbcf07", "TARGET@v11_hash=55db7dbcdd99afb975f581d14d63565b", "TARGET@v11_hash=6e450df11921bc33a786449bac938c9d", "TARGET@v11_hash=a8c5906b762b242629804361b9033863", "TARGET@v11_hash=7c2226886327ec22c77df2302ee2ee78", "TARGET@v11_hash=8590b46f2c96916896d957b3f2210722", "TARGET@v11_hash=a2a30da78006076ae735095728c455fb", "TARGET@v11_hash=0b18d32898a17c0e231ac53c693a419e"]}