// lib/providers/app_provider.dart

import 'dart:async';

import 'package:aslaa/constant.dart';
import 'package:aslaa/exceptions/http_result_message.dart';
import 'package:aslaa/flutter_flow/flutter_flow_util.dart';
import 'package:aslaa/main.dart';
import 'package:aslaa/models/device.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/mqtt/mqtt_websocket.dart';
import 'package:aslaa/service/api_service.dart';
import 'package:aslaa/service/bluetooth_service.dart' as my_bt;
import 'package:aslaa/models/two_factor_auth.dart';
import 'package:aslaa/exceptions/two_factor_required_exception.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class AppProvider extends ChangeNotifier with WidgetsBindingObserver {
  User? user;
  MqttHandler? mqttHandler;
  DeviceStatus ds = DeviceStatus.initial();
  AppLifecycleState _appLifecycleState = AppLifecycleState.resumed;

  late my_bt.BluetoothService bluetoothService;

  bool get isAuthenticated => user != null;
  String get authRole => user?.role ?? '';
  User? get authClient => user;

  // Getter for raw message stream
  Stream<String>? get rawMessageStream => mqttHandler?.rawMessageStream;
  ApiService apiService = ApiService(API_HOST);

  String? simBalance;
  String? simExpiredDate;
  Map<String, dynamic>? weatherData;

  // Add a StreamSubscription for SMS messages
  StreamSubscription<String>? _smsSubscription;

  // Add a StreamSubscription for raw MQTT messages
  StreamSubscription<String>? _rawMessageSubscription;

  AppProvider() {
    WidgetsBinding.instance.addObserver(this);
    bluetoothService = my_bt.BluetoothService();
    print('AppProvider:: BluetoothService initialized');

    // Ensure any existing MQTT connection is disconnected
    mqttHandler?.disconnect();
    mqttHandler?.dispose();
    mqttHandler = null;

    _autoReconnect();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _appLifecycleState = state;
    updateMqttConnection(); // Calls the new method to handle MQTT connection state
  }

  void updateMqttConnection() {
    if (mqttHandler == null) {
      print('AppProvider:: MQTT handler is not initialized.');
      return;
    }

    if (_appLifecycleState == AppLifecycleState.resumed) {
      if (!mqttHandler!.isConnected) {
        print('AppProvider:: App is resumed, connecting MQTT.');
        mqttHandler!.connect();
      }
    } else if (_appLifecycleState == AppLifecycleState.inactive ||
        _appLifecycleState == AppLifecycleState.paused) {
      if (mqttHandler!.isConnected) {
        print('AppProvider:: App is paused or inactive, disconnecting MQTT.');
        mqttHandler!.disconnect();
      }
    }
  }

  void updateDeviceStatus(DeviceStatus newStatus) {
    ds = newStatus;
    print('AppProvider:: DeviceStatus updated via updateDeviceStatus');
    notifyListeners();
  }

  Future<void> fetchSimStatus() async {
    String? deviceNumber = user?.device?.deviceNumber;

    if (deviceNumber != null && deviceNumber.isNotEmpty) {
      print('AppProvider:: Fetching SIM status for device $deviceNumber');
      final data = await apiService.fetchSimStatus(deviceNumber);
      if (data != null) {
        simBalance = data['balance'];
        simExpiredDate = data['expiredDate'];
        print(
            'AppProvider:: SIM status fetched: Balance $simBalance, Expiry $simExpiredDate');
        notifyListeners();
      } else {
        debugPrint("AppProvider:: Failed to fetch SIM status.");
      }
    } else {
      debugPrint("AppProvider:: Device number is null or empty.");
    }
  }

  Future<void> checkSimCard(String deviceNumber) async {
    print('AppProvider:: Checking SIM card for device $deviceNumber');
    final result = await apiService.checkSimCard(deviceNumber);
    if (result) {
      debugPrint("AppProvider:: SIM card check successful.");
    } else {
      debugPrint("AppProvider:: SIM card check failed.");
    }
  }

  Future<void> fetchWeatherData(String location) async {
    print('AppProvider:: Fetching weather data for location $location');
    final data = await apiService.fetchWeatherData(location);
    if (data != null) {
      weatherData = data;
      print('AppProvider:: Weather data fetched');
      notifyListeners();
    } else {
      debugPrint('AppProvider:: Failed to fetch weather data.');
    }
  }

  void handleAppResumed() {
    if (mqttHandler != null && !mqttHandler!.isConnected) {
      print('AppProvider:: App resumed, reconnecting MQTT.');
      mqttHandler!.connect();
    }
  }

  void handleAppPaused() {
    if (mqttHandler != null && mqttHandler!.isConnected) {
      print('AppProvider:: App paused, disconnecting MQTT.');
      mqttHandler!.disconnect();
    }
  }

  void _onMqttDisconnected() {
    debugPrint('AppProvider:: MqttHandler disconnected');
    if (_appLifecycleState == AppLifecycleState.resumed) {
      _attemptReconnect();
    } else {
      print('AppProvider:: App is not active, not attempting to reconnect.');
    }
    notifyListeners();
  }

  void updateWallet({String bankName = "", String bankAccount = ""}) {
    if (user != null) {
      user!.wallet.bankAccount = bankAccount;
      user!.wallet.bankName = bankName;
      print('AppProvider:: Wallet updated: $bankName, $bankAccount');
      notifyListeners();
    } else {
      debugPrint('AppProvider:: Cannot update wallet, user is null.');
    }
  }

  void updatePassword({String password = ""}) {
    if (user != null) {
      user!.password = password;
      print('AppProvider:: Password updated.');
      notifyListeners();
    } else {
      debugPrint('AppProvider:: Cannot update password, user is null.');
    }
  }

  Future<void> reload() async {
    if (user != null) {
      print('AppProvider:: Reloading user data.');
      await login(user!.phoneNumber, user!.password);
    } else {
      debugPrint('AppProvider:: Cannot reload, user is null.');
    }
  }

  void loginAsGuest() {
    user = User.fromGuest();
    print('AppProvider:: Logged in as guest.');
    notifyListeners();
  }

  Future<void> loginWithToken(String token) async {
    print('AppProvider:: Logging in with token.');
    Map<String, String> headers = {
      'Content-Type': 'application/json; charset=UTF-8',
      'Authorization': 'Bearer $token',
    };

    try {
      final response = await http.get(
        Uri.parse('$API_HOST/api/auth/my-account'),
        headers: headers,
      );

      print(
          'AppProvider:: loginWithToken response status: ${response.statusCode}');
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);

        if (json['user'] != null || json['success'] == true) {
          user = User.fromJson(json, token);
          print('AppProvider:: User logged in with token.');

          // Check if user has a device before initializing MQTT
          if (user!.device != null) {
            print('AppProvider:: User has a device. Initializing MQTT.');
            await _initializeMqttHandler();
          } else {
            print(
                'AppProvider:: User does not have a device. Skipping MQTT initialization.');
          }

          notifyListeners();
        } else {
          debugPrint('AppProvider:: Token expired or invalid.');
          throw HttpResultException(
            message: 'Token expired or invalid',
            statusCode: 200,
            title: 'Login Failed',
            success: false,
          );
        }
      } else {
        throw HttpResultException(
          message: 'Failed to login through API, ${response.statusCode} error',
          statusCode: response.statusCode,
          title: 'Internal Server Error',
          success: false,
        );
      }
    } catch (err) {
      debugPrint('AppProvider:: Error during loginWithToken: $err');
    }
  }

  Future<void> _autoReconnect() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');

    if (token != null) {
      print('AppProvider:: Token found, attempting auto-reconnect.');
      await loginWithToken(token);
    } else {
      print('AppProvider:: No token found, cannot auto-reconnect.');
    }
  }

  Future<void> login(String? phoneNumber, String? password) async {
    print('AppProvider:: Attempting login with phoneNumber: $phoneNumber');
    try {
      final response = await http.post(
        Uri.parse('$API_HOST/api/auth/pincode'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(
          {'phoneNumber': phoneNumber ?? "", 'pinCode': password ?? ""},
        ),
      );

      print('AppProvider:: login response status: ${response.statusCode}');
      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);

        // Check if 2FA is required first (before checking success)
        if (json['requires2FA'] == true) {
          print('AppProvider:: 2FA is required for this user.');
          throw TwoFactorRequiredException(
            phoneNumber: json['phoneNumber'] ?? phoneNumber ?? '',
            message: json['message'] ?? 'Two-factor authentication is required',
            title: '2FA Required',
          );
        }

        if (json['success']) {
          user = User.fromJson(json, null);

          SharedPreferences prefs = await SharedPreferences.getInstance();
          prefs.setString('token', user!.token);
          print('AppProvider:: User logged in successfully.');

          // Check if user has a device before initializing MQTT
          if (user!.device != null) {
            print('AppProvider:: User has a device. Initializing MQTT.');
            await _initializeMqttHandler();
          } else {
            print(
                'AppProvider:: User does not have a device. Skipping MQTT initialization.');
          }

          notifyListeners();
        } else {
          String message = json['message'] ?? 'Failed to login, try again';
          debugPrint('AppProvider:: Login failed: $message');
          throw HttpResultException(
            message: message,
            statusCode: 200,
            title: 'Login Failed',
            success: false,
          );
        }
      } else {
        throw HttpResultException(
          message: 'Failed to login through API, ${response.statusCode} error',
          statusCode: response.statusCode,
          title: 'Internal Server Error',
          success: false,
        );
      }
    } catch (err) {
      debugPrint('AppProvider:: Exception during login: $err');
      if (err is! HttpResultException) {
        throw HttpResultException(
          message: 'Failed to connect to server',
          statusCode: 0,
          title: 'Network Error',
          success: false,
        );
      } else {
        rethrow;
      }
    }
  }

  /// Complete login after successful 2FA verification
  Future<void> completeLoginAfter2FA(Map<String, dynamic> loginData) async {
    try {
      print('AppProvider:: Completing login after 2FA verification.');

      // Extract user data from the login response
      if (loginData['user'] != null && loginData['token'] != null) {
        user = User.fromJson(loginData, loginData['token']);

        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString('token', user!.token);
        print('AppProvider:: User logged in successfully after 2FA.');

        // Check if user has a device before initializing MQTT
        if (user!.device != null) {
          print('AppProvider:: User has a device. Initializing MQTT.');
          await _initializeMqttHandler();
        } else {
          print(
              'AppProvider:: User does not have a device. Skipping MQTT initialization.');
        }

        notifyListeners();
      } else {
        throw HttpResultException(
          message: 'Invalid login data received after 2FA verification',
          statusCode: 200,
          title: 'Login Failed',
          success: false,
        );
      }
    } catch (err) {
      debugPrint('AppProvider:: Exception during 2FA login completion: $err');
      rethrow;
    }
  }

  Future<void> _initializeMqttHandler() async {
    if (user == null || user!.device == null) {
      print(
          'AppProvider:: Cannot initialize MQTT handler; user or device is null.');
      return;
    }

    // Disconnect any existing MQTT connection
    await mqttHandler?.disconnect();
    await mqttHandler?.dispose();
    mqttHandler = null;

    print('AppProvider:: Initializing MQTT handler.');

    // Initialize MQTT handler without awaiting connection result
    mqttHandler = await MqttHandler.create(
      phoneNumber: user!.phoneNumber,
      userDevice: user!.device!.deviceNumber,
      device: user!.device,
      onConnectedCallback: _onMqttConnected,
      onDisconnectedCallback: _onMqttDisconnected,
    );

    mqttHandler!.data.addListener(_onDeviceStatusUpdated);
    print('AppProvider:: Listener added to mqttHandler.data');

    // Subscribe to SMS messages
    _smsSubscription?.cancel();
    _smsSubscription = mqttHandler!.smsStream.listen(_onSmsReceived);
    print('AppProvider:: Subscribed to SMS messages');

    // Attempt to connect in the background and log result, without blocking
    mqttHandler!.connect().then((connected) {
      if (connected) {
        print('AppProvider:: MQTT connected successfully.');
      } else {
        print('AppProvider:: MQTT connection failed.');
        // Optionally, initiate reconnection attempts without blocking the UI
        _attemptReconnect();
      }
    });
  }

  void _onDeviceStatusUpdated() {
    if (mqttHandler == null) {
      return;
    }
    ds = mqttHandler!.data.value;
    print('AppProvider:: DeviceStatus updated: ${ds.toJson()}');
    notifyListeners();
    print('AppProvider:: notifyListeners() called');
  }

  void _onMqttConnected() {
    debugPrint('AppProvider:: MqttHandler connected');
    notifyListeners();
  }

  void _attemptReconnect({int retryCount = 0}) {
    const maxRetries = 5;
    if (retryCount >= maxRetries) {
      debugPrint('AppProvider:: Max MQTT reconnection attempts reached');
      return;
    }
    final delay = Duration(seconds: 2 << retryCount); // Exponential backoff
    Future.delayed(delay, () async {
      if (mqttHandler == null) {
        return;
      }
      debugPrint(
          'AppProvider:: Attempting MQTT reconnection (Attempt ${retryCount + 1})');
      bool connected = await mqttHandler!.connect();
      if (!connected) {
        _attemptReconnect(retryCount: retryCount + 1);
      } else {
        debugPrint('AppProvider:: MQTT reconnected successfully');
        notifyListeners();
      }
    });
  }

  void updateUserDevice(Device? newDevice) {
    user?.device = newDevice;
    if (newDevice != null) {
      print('AppProvider:: User device updated. Initializing MQTT.');
      _initializeMqttHandler();
    } else {
      print('AppProvider:: User device removed. Disposing MQTT.');
      mqttHandler?.disconnect();
      mqttHandler?.dispose();
      mqttHandler = null;
    }
    notifyListeners();
  }

  Future<void> logout() async {
    print('AppProvider:: Logging out.');

    // Ensure MQTT is properly disconnected and disposed
    if (mqttHandler != null) {
      await mqttHandler!.disconnect();
      await mqttHandler!.dispose();
      mqttHandler = null;
    }

    // Clear device status
    ds = DeviceStatus.initial();

    // Clear user data
    user = null;
    simBalance = null;
    simExpiredDate = null;
    weatherData = null;

    // Remove token from shared preferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');

    notifyListeners();
  }

  // Add a method to handle SMS messages
  void _onSmsReceived(String smsMessage) {
    print('AppProvider:: SMS received: $smsMessage');

    // Use a GlobalKey to access the ScaffoldMessenger
    if (navigatorKey.currentContext != null) {
      showSnackbar(
        navigatorKey.currentContext!,
        'СИМ мэдээлэл: $smsMessage',
        duration: 5,
      );
    }

    // If the message contains balance information, update the simBalance
    if (smsMessage.contains('TG') && smsMessage.contains('hugatsaa')) {
      // Try to extract balance and expiry date
      try {
        // Extract balance (assuming format like "Tand 16370.0 TG.")
        RegExp balanceRegex = RegExp(r'Tand (\d+\.?\d*) TG');
        var balanceMatch = balanceRegex.firstMatch(smsMessage);
        if (balanceMatch != null && balanceMatch.groupCount >= 1) {
          simBalance = '${balanceMatch.group(1)} ₮';
        }

        // Extract expiry date (assuming format like "Huchintei hugatsaa 2025/09/22 duustal")
        RegExp dateRegex = RegExp(r'hugatsaa (\d{4}/\d{2}/\d{2})');
        var dateMatch = dateRegex.firstMatch(smsMessage);
        if (dateMatch != null && dateMatch.groupCount >= 1) {
          simExpiredDate = dateMatch.group(1);
        }

        notifyListeners();
      } catch (e) {
        print('AppProvider:: Error parsing SMS: $e');
      }
    }
  }

  @override
  void dispose() {
    _smsSubscription?.cancel();
    mqttHandler?.dispose();
    super.dispose();
  }
}
