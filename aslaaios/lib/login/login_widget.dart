import 'package:aslaa/exceptions/http_result_message.dart';
import 'package:aslaa/exceptions/two_factor_required_exception.dart';
import 'package:aslaa/components/two_factor_verification.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../flutter_flow/flutter_flow_animations.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import '../flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_animate/flutter_animate.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';

import '../constant.dart';

class LoginWidget extends StatefulWidget {
  const LoginWidget({Key? key}) : super(key: key);

  @override
  _LoginWidgetState createState() => _LoginWidgetState();
}

class _LoginWidgetState extends State<LoginWidget>
    with TickerProviderStateMixin {
  final animationsMap = {
    'rowOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
        ScaleEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.4,
          end: 1,
        ),
      ],
    ),
    'textOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 300.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
      ],
    ),
    'columnOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
        MoveEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: Offset(100, 0),
          end: Offset(0, 0),
        ),
      ],
    ),
    'buttonOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        ScaleEffect(
          curve: Curves.easeInOut,
          delay: 300.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
      ],
    ),
  };
  TextEditingController? mobileController;
  TextEditingController? passwordController;
  late bool passwordVisibility;
  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();

    mobileController = TextEditingController();
    passwordController = TextEditingController();
    passwordVisibility = false;
    if (mounted)
      Future.delayed(Duration.zero, () async {
        SharedPreferences _prefs = await SharedPreferences.getInstance();
        String? token = _prefs.getString('token');
        if (token != null) {
          AppProvider provider =
              Provider.of<AppProvider>(context, listen: false);

          // Check if user is already logged in to prevent double login
          if (provider.user == null) {
            print('LOGIN_INIT:: Found token, attempting auto-login');
            try {
              await provider.loginWithToken(token);
              print('LOGIN_INIT:: Auto-login successful, navigating to main');
              context.pushNamed('main');
            } catch (err) {
              print('LOGIN_INIT:: Auto-login failed: $err');
              debugPrint('token expired or network error');
            }
          } else {
            print('LOGIN_INIT:: User already logged in, skipping auto-login');
          }
        } else {
          print('LOGIN_INIT:: No token found');
        }
      });
  }

  @override
  void dispose() {
    mobileController?.dispose();
    passwordController?.dispose();
    super.dispose();
  }

  Future<void> saveFcmTokenToDatabase(String userid, String fcmToken) async {
    final url = Uri.parse('$API_HOST/api/auth/savefcmtoken');
    final body = {'userid': userid, 'fcmToken': fcmToken};
    final response = await http.post(url, body: body);
    if (response.statusCode == 200) {
      debugPrint('FCM token saved successfully');
    } else {
      debugPrint('Failed to save FCM token to database');
      throw Exception('Failed to save FCM token to database');
    }
  }

  Future login(AppProvider authProvider) async {
    try {
      if (mobileController?.text == null || mobileController?.text == "")
        return;
      await authProvider.login(
          mobileController?.text, passwordController?.text);

      // Get the FCM token for the user's device
      String? fcmToken = await FirebaseMessaging.instance.getToken();
      // Save the FCM token to the server's database, along with the user's ID
// Save the FCM token to the server's database, along with the user's ID
      await saveFcmTokenToDatabase(authProvider.user?.id ?? "", fcmToken!);

      context.pushNamed('main');
    } catch (err) {
      if (err is TwoFactorRequiredException) {
        // Show 2FA verification dialog
        _show2FADialog(err.phoneNumber, authProvider);
      } else if (err is HttpResultException) {
        final HttpResultException _err = err;
        final ContentType contentType =
            _err.statusCode == 200 ? ContentType.warning : ContentType.failure;
        showAnimatedSnackbar(context, _err.message, _err.title, contentType);
      }

      debugPrint('hmmm---$err');
    }
  }

  void _show2FADialog(String phoneNumber, AppProvider authProvider) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => TwoFactorVerificationDialog(
        phoneNumber: phoneNumber,
        onSuccess: (loginData) async {
          print('LOGIN_2FA:: 2FA success callback called');
          Navigator.of(context).pop();
          print('LOGIN_2FA:: Dialog closed');

          try {
            // Complete the login process with the received data
            print('LOGIN_2FA:: Calling completeLoginAfter2FA');
            await authProvider.completeLoginAfter2FA(loginData);
            print('LOGIN_2FA:: completeLoginAfter2FA completed successfully');

            // Get the FCM token for the user's device
            String? fcmToken = await FirebaseMessaging.instance.getToken();
            print('LOGIN_2FA:: FCM token obtained: ${fcmToken != null}');

            // Save the FCM token to the server's database
            await saveFcmTokenToDatabase(
                authProvider.user?.id ?? "", fcmToken!);
            print('LOGIN_2FA:: FCM token saved successfully');

            print('LOGIN_2FA:: Navigating to main page');

            // Ensure the provider state is updated
            print(
                'LOGIN_2FA:: Provider user state: ${authProvider.user != null}');
            print(
                'LOGIN_2FA:: Provider device state: ${authProvider.user?.device != null}');
            print('LOGIN_2FA:: User ID: ${authProvider.user?.id}');
            print(
                'LOGIN_2FA:: Device Number: ${authProvider.user?.device?.deviceNumber}');

            try {
              print(
                  'LOGIN_2FA:: Current route: ${GoRouterState.of(context).uri.toString()}');
              print(
                  'LOGIN_2FA:: Current route name: ${GoRouterState.of(context).name}');
              print(
                  'LOGIN_2FA:: Current route path: ${GoRouterState.of(context).path}');
            } catch (e) {
              print('LOGIN_2FA:: Could not get router state: $e');
            }

            // Add a delay to ensure dialog is fully closed and state is updated
            await Future.delayed(const Duration(milliseconds: 500));

            // Check if we can access the router
            try {
              final router = GoRouter.of(context);
              print('LOGIN_2FA:: Router accessible: true');
              print(
                  'LOGIN_2FA:: Router current location: ${router.routerDelegate.currentConfiguration.uri.toString()}');
            } catch (e) {
              print('LOGIN_2FA:: Could not access router: $e');
            }

            // Comprehensive navigation approach
            bool navigationSuccessful = false;

            // Method 1: Try context.go with different paths
            if (!navigationSuccessful) {
              try {
                print('LOGIN_2FA:: Attempting context.go("/main")');
                context.go('/main');
                await Future.delayed(const Duration(milliseconds: 100));
                print('LOGIN_2FA:: context.go("/main") completed');
                navigationSuccessful = true;
              } catch (e) {
                print('LOGIN_2FA:: context.go("/main") failed: $e');
              }
            }

            // Method 1b: Try navigating to root first, then main
            if (!navigationSuccessful) {
              try {
                print('LOGIN_2FA:: Attempting context.go("/") then "/main"');
                context.go('/');
                await Future.delayed(const Duration(milliseconds: 100));
                context.go('/main');
                await Future.delayed(const Duration(milliseconds: 100));
                print('LOGIN_2FA:: Two-step navigation completed');
                navigationSuccessful = true;
              } catch (e) {
                print('LOGIN_2FA:: Two-step navigation failed: $e');
              }
            }

            // Method 2: Try context.pushNamed
            if (!navigationSuccessful) {
              try {
                print('LOGIN_2FA:: Attempting context.pushNamed("main")');
                context.pushNamed('main');
                await Future.delayed(const Duration(milliseconds: 100));
                print('LOGIN_2FA:: context.pushNamed("main") completed');
                navigationSuccessful = true;
              } catch (e) {
                print('LOGIN_2FA:: context.pushNamed("main") failed: $e');
              }
            }

            // Method 3: Try Navigator.pushReplacementNamed
            if (!navigationSuccessful) {
              try {
                print('LOGIN_2FA:: Attempting Navigator.pushReplacementNamed');
                Navigator.of(context).pushReplacementNamed('/main');
                await Future.delayed(const Duration(milliseconds: 100));
                print('LOGIN_2FA:: Navigator.pushReplacementNamed completed');
                navigationSuccessful = true;
              } catch (e) {
                print('LOGIN_2FA:: Navigator.pushReplacementNamed failed: $e');
              }
            }

            // Method 4: Try Navigator.pushNamedAndRemoveUntil
            if (!navigationSuccessful) {
              try {
                print(
                    'LOGIN_2FA:: Attempting Navigator.pushNamedAndRemoveUntil');
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/main',
                  (Route<dynamic> route) => false,
                );
                await Future.delayed(const Duration(milliseconds: 100));
                print(
                    'LOGIN_2FA:: Navigator.pushNamedAndRemoveUntil completed');
                navigationSuccessful = true;
              } catch (e) {
                print(
                    'LOGIN_2FA:: Navigator.pushNamedAndRemoveUntil failed: $e');
              }
            }

            // Method 5: Force navigation by rebuilding the entire app state
            if (!navigationSuccessful) {
              print(
                  'LOGIN_2FA:: All navigation methods failed, forcing app state rebuild');
              try {
                // Force a complete app state refresh by updating a property
                authProvider.updateDeviceStatus(authProvider.ds);
                await Future.delayed(const Duration(milliseconds: 200));

                // Try context.go one more time after state refresh
                context.go('/main');
                print(
                    'LOGIN_2FA:: Forced navigation after state refresh completed');
                navigationSuccessful = true;
              } catch (e) {
                print('LOGIN_2FA:: Forced navigation failed: $e');
              }
            }

            // Method 6: Try using root navigator context
            if (!navigationSuccessful) {
              print('LOGIN_2FA:: Trying root navigator context');
              try {
                final rootContext =
                    Navigator.of(context, rootNavigator: true).context;
                rootContext.go('/main');
                await Future.delayed(const Duration(milliseconds: 100));
                print('LOGIN_2FA:: Root navigator navigation completed');
                navigationSuccessful = true;
              } catch (e) {
                print('LOGIN_2FA:: Root navigator navigation failed: $e');
              }
            }

            // Method 7: Use AppProvider's dedicated navigation method
            if (!navigationSuccessful) {
              print(
                  'LOGIN_2FA:: Using AppProvider navigation method as final fallback');
              try {
                authProvider.navigateToMainPage(context);
                navigationSuccessful = true;
                print('LOGIN_2FA:: AppProvider navigation method completed');
              } catch (e) {
                print('LOGIN_2FA:: AppProvider navigation method failed: $e');
              }
            }

            print(
                'LOGIN_2FA:: Navigation attempt completed. Success: $navigationSuccessful');

            // If all navigation methods failed, show an error message
            if (!navigationSuccessful) {
              print(
                  'LOGIN_2FA:: All navigation methods failed - showing error');
              showAnimatedSnackbar(
                context,
                'Login successful but navigation failed. Please restart the app.',
                'Navigation Error',
                ContentType.warning,
              );
            }
          } catch (err) {
            print('LOGIN_2FA:: Error completing 2FA login: $err');
            debugPrint('Error completing 2FA login: $err');
            if (err is HttpResultException) {
              final HttpResultException _err = err;
              final ContentType contentType = _err.statusCode == 200
                  ? ContentType.warning
                  : ContentType.failure;
              showAnimatedSnackbar(
                  context, _err.message, _err.title, contentType);
            }
          }
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  loginAsGuest(AppProvider authProvider) {
    authProvider.loginAsGuest();
    context.pushNamed('main');
  }

  @override
  Widget build(BuildContext context) {
    AppProvider authProvider = Provider.of<AppProvider>(context, listen: false);

    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding:
                                  EdgeInsetsDirectional.fromSTEB(0, 60, 0, 24),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Align(
                                    alignment: AlignmentDirectional(0, 0),
                                    child: Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0, 24, 0, 24),
                                      child: Text(
                                        FFLocalizations.of(context).getText(
                                          'u25ci76l' /* WELCOME TO VISIT REMOTE
CAR CO... */
                                          ,
                                        ),
                                        textAlign: TextAlign.center,
                                        maxLines: 2,
                                        style: FlutterFlowTheme.of(context)
                                            .title1
                                            .override(
                                              fontFamily: 'Roboto',
                                              fontSize: 22,
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                    ),
                                  ),
                                  Image.asset(
                                    'assets/images/Porsche-Taycan-Transparent-PNG.png',
                                    width: 260,
                                    height: 160,
                                    fit: BoxFit.fitWidth,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ).animateOnPageLoad(
                            animationsMap['rowOnPageLoadAnimation']!),
                      ),
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(24, 0, 24, 0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    '9ctw0pwj' /* Login to access your account b... */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .subtitle2
                                      .override(
                                        fontFamily: 'Roboto',
                                        fontSize: 16,
                                        fontWeight: FontWeight.normal,
                                      ),
                                ).animateOnPageLoad(
                                    animationsMap['textOnPageLoadAnimation']!),
                              ],
                            ),
                            Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0, 20, 0, 0),
                                  child: TextFormField(
                                    controller: mobileController,
                                    obscureText: false,
                                    decoration: InputDecoration(
                                      labelText:
                                          FFLocalizations.of(context).getText(
                                        'kczusika' /* Mobile Number */,
                                      ),
                                      hintText:
                                          FFLocalizations.of(context).getText(
                                        '6rxw5d4j' /* Input your mobile number */,
                                      ),
                                      hintStyle: FlutterFlowTheme.of(context)
                                          .bodyText2,
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Color(0x00000000), width: 1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      filled: true,
                                      fillColor: FlutterFlowTheme.of(context)
                                          .secondaryBackground,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyText1
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontWeight: FontWeight.normal,
                                        ),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0, 20, 0, 0),
                                  child: TextFormField(
                                    controller: passwordController,
                                    obscureText: !passwordVisibility,
                                    decoration: InputDecoration(
                                      labelText:
                                          FFLocalizations.of(context).getText(
                                        'vfesrn0p' /* Password */,
                                      ),
                                      hintText:
                                          FFLocalizations.of(context).getText(
                                        'zm8n1sm3' /* Input your password */,
                                      ),
                                      hintStyle: FlutterFlowTheme.of(context)
                                          .bodyText2,
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Color(0x00000000),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      filled: true,
                                      fillColor: FlutterFlowTheme.of(context)
                                          .secondaryBackground,
                                      suffixIcon: InkWell(
                                        onTap: () => setState(
                                          () => passwordVisibility =
                                              !passwordVisibility,
                                        ),
                                        focusNode:
                                            FocusNode(skipTraversal: true),
                                        child: Icon(
                                          passwordVisibility
                                              ? Icons.visibility_outlined
                                              : Icons.visibility_off_outlined,
                                          color: Color(0xFF757575),
                                          size: 20,
                                        ),
                                      ),
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyText1
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontWeight: FontWeight.normal,
                                        ),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0, 24, 0, 24),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      InkWell(
                                        child: Text(
                                          FFLocalizations.of(context).getText(
                                            'tjshpu6l' /* Forgot Password? */,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .subtitle2
                                              .override(
                                                fontFamily: 'Roboto',
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryColor,
                                                fontWeight: FontWeight.normal,
                                              ),
                                        ),
                                        onTap: () {
                                          context.pushNamed('forgetPassword');
                                        },
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            8, 0, 0, 0),
                                        child: FFButtonWidget(
                                          onPressed: () async {
                                            await login(authProvider);
                                            // context.pushNamed('main');
                                          },
                                          text: FFLocalizations.of(context)
                                              .getText(
                                            'a97y7eaf' /* Login */,
                                          ),
                                          options: FFButtonOptions(
                                            width: 130,
                                            height: 40,
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryColor,
                                            textStyle:
                                                FlutterFlowTheme.of(context)
                                                    .subtitle2
                                                    .override(
                                                      fontFamily: 'Roboto',
                                                      color: Colors.white,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                            borderSide: BorderSide(
                                              color: Colors.transparent,
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(20),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  width:
                                      MediaQuery.of(context).size.width * 0.8,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryBackground,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        FFLocalizations.of(context).getText(
                                          'lx7xgiid' /* Don't have account? */,
                                        ),
                                        style: FlutterFlowTheme.of(context)
                                            .bodyText1
                                            .override(
                                              fontFamily: 'Roboto',
                                              fontWeight: FontWeight.normal,
                                            ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            24, 0, 4, 0),
                                        child: InkWell(
                                          onTap: () async {
                                            // onGoRegister

                                            context.pushNamed('register');
                                          },
                                          child: Text(
                                            FFLocalizations.of(context).getText(
                                              'tulayb4h' /* Create */,
                                            ),
                                            style: FlutterFlowTheme.of(context)
                                                .bodyText1
                                                .override(
                                                  fontFamily: 'Roboto',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .secondaryColor,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                          ),
                                        ),
                                      ),
                                      InkWell(
                                        onTap: () async {
                                          // onGoRegister

                                          context.pushNamed('register');
                                        },
                                        child: Icon(
                                          Icons.arrow_forward,
                                          color: FlutterFlowTheme.of(context)
                                              .secondaryColor,
                                          size: 24,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ).animateOnPageLoad(
                                animationsMap['columnOnPageLoadAnimation']!),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(0, 40, 0, 40),
                        child: FFButtonWidget(
                          onPressed: () {
                            loginAsGuest(authProvider);
                          },
                          text: FFLocalizations.of(context).getText(
                            '8e2vmjzx' /* Continue as Guest */,
                          ),
                          options: FFButtonOptions(
                            width: 200,
                            height: 40,
                            color: FlutterFlowTheme.of(context)
                                .secondaryBackground,
                            textStyle: FlutterFlowTheme.of(context)
                                .subtitle2
                                .override(
                                  fontFamily: 'Roboto',
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  fontWeight: FontWeight.w500,
                                ),
                            borderSide: BorderSide(
                              color: Colors.transparent,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ).animateOnPageLoad(
                            animationsMap['buttonOnPageLoadAnimation']!),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
