// lib/components/two_factor_setup.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../models/two_factor_auth.dart';
import '../service/two_factor_service.dart';
import '../utils/totp_utils.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_widgets.dart';

class TwoFactorSetupDialog extends StatefulWidget {
  final String userToken;
  final VoidCallback? onComplete;
  final VoidCallback? onCancel;

  const TwoFactorSetupDialog({
    Key? key,
    required this.userToken,
    this.onComplete,
    this.onCancel,
  }) : super(key: key);

  @override
  State<TwoFactorSetupDialog> createState() => _TwoFactorSetupDialogState();
}

class _TwoFactorSetupDialogState extends State<TwoFactorSetupDialog> {
  final TwoFactorService _twoFactorService = TwoFactorService();
  final PageController _pageController = PageController();

  TwoFactorSetupState _setupState = TwoFactorSetupState(
    currentStep: TwoFactorSetupStep.setup,
  );

  String _verificationCode = '';
  bool _showManualEntry = false;

  @override
  void initState() {
    super.initState();
    _initiate2FASetup();
  }

  Future<void> _initiate2FASetup() async {
    print('TwoFactorSetupDialog: _initiate2FASetup called');
    setState(() {
      _setupState = _setupState.copyWith(isLoading: true, error: null);
    });

    try {
      final response = await _twoFactorService.setup2FA(widget.userToken);
      print(
          'TwoFactorSetupDialog: Setup response - Success: ${response.success}');

      if (response.success && response.data != null) {
        print('TwoFactorSetupDialog: Setup successful, moving to verify step');
        setState(() {
          _setupState = _setupState.copyWith(
            setupResponse: response.data,
            isLoading: false,
            currentStep: TwoFactorSetupStep.verify,
          );
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        print('TwoFactorSetupDialog: Setup failed - ${response.message}');
        setState(() {
          _setupState = _setupState.copyWith(
            isLoading: false,
            error: response.message,
          );
        });
      }
    } catch (error) {
      print('TwoFactorSetupDialog: Exception caught - $error');
      setState(() {
        _setupState = _setupState.copyWith(
          isLoading: false,
          error: 'Failed to setup 2FA: $error',
        );
      });
    }
  }

  Future<void> _verifyAndEnable2FA() async {
    if (_verificationCode.length != 6) {
      setState(() {
        _setupState = _setupState.copyWith(
          error: 'Please enter a valid 6-digit code',
        );
      });
      return;
    }

    setState(() {
      _setupState = _setupState.copyWith(isLoading: true, error: null);
    });

    try {
      final response = await _twoFactorService.enable2FA(
        widget.userToken,
        _verificationCode,
      );

      if (response.success && response.data != null) {
        setState(() {
          _setupState = _setupState.copyWith(
            backupCodes: response.data!.backupCodes,
            isLoading: false,
            currentStep: TwoFactorSetupStep.backupCodes,
          );
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        setState(() {
          _setupState = _setupState.copyWith(
            isLoading: false,
            error: response.message,
          );
        });
      }
    } catch (error) {
      setState(() {
        _setupState = _setupState.copyWith(
          isLoading: false,
          error: 'Failed to enable 2FA: $error',
        );
      });
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.security,
                  color: FlutterFlowTheme.of(context).primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Setup Two-Factor Authentication',
                    style: FlutterFlowTheme.of(context).title2,
                  ),
                ),
                IconButton(
                  onPressed: widget.onCancel,
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Step indicator
            _buildStepIndicator(),
            const SizedBox(height: 24),

            // Content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildSetupStep(),
                  _buildVerificationStep(),
                  _buildBackupCodesStep(),
                ],
              ),
            ),

            // Error message
            if (_setupState.error != null)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _setupState.error!,
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      children: [
        _buildStepCircle(1, TwoFactorSetupStep.setup),
        _buildStepLine(),
        _buildStepCircle(2, TwoFactorSetupStep.verify),
        _buildStepLine(),
        _buildStepCircle(3, TwoFactorSetupStep.backupCodes),
      ],
    );
  }

  Widget _buildStepCircle(int stepNumber, TwoFactorSetupStep step) {
    final isActive = _setupState.currentStep == step;
    final isCompleted = _setupState.currentStep.index > step.index;

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isCompleted
            ? FlutterFlowTheme.of(context).primaryColor
            : isActive
                ? FlutterFlowTheme.of(context).primaryColor
                : Colors.grey.shade300,
      ),
      child: Center(
        child: isCompleted
            ? Icon(Icons.check, color: Colors.white, size: 18)
            : Text(
                stepNumber.toString(),
                style: TextStyle(
                  color: isActive ? Colors.white : Colors.grey.shade600,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildStepLine() {
    return Expanded(
      child: Container(
        height: 2,
        color: Colors.grey.shade300,
      ),
    );
  }

  Widget _buildSetupStep() {
    if (_setupState.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Setting up 2FA...'),
          ],
        ),
      );
    }

    return const Center(
      child: Text('Initializing 2FA setup...'),
    );
  }

  Widget _buildVerificationStep() {
    final setupResponse = _setupState.setupResponse;
    if (setupResponse == null) return const SizedBox();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Scan QR Code',
            style: FlutterFlowTheme.of(context).title2,
          ),
          const SizedBox(height: 16),

          Text(
            'Scan this QR code with your Google Authenticator app',
            style: FlutterFlowTheme.of(context).bodyText1,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // QR Code
          if (!_showManualEntry) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: QrImageView(
                data: setupResponse.qrCode.contains('data:image')
                    ? TOTPUtils.generateQRCodeUrl(
                        setupResponse.secret,
                        'User Account',
                        'ASLAA Car Control',
                      )
                    : setupResponse.qrCode,
                version: QrVersions.auto,
                size: 200.0,
              ),
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => setState(() => _showManualEntry = true),
              child: const Text('Can\'t scan? Enter code manually'),
            ),
          ] else ...[
            // Manual entry
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Manual Entry Key:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  SelectableText(
                    TOTPUtils.formatSecretForDisplay(
                        setupResponse.manualEntryKey),
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: () =>
                        _copyToClipboard(setupResponse.manualEntryKey),
                    icon: const Icon(Icons.copy, size: 16),
                    label: const Text('Copy'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          FlutterFlowTheme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            TextButton(
              onPressed: () => setState(() => _showManualEntry = false),
              child: const Text('Show QR code instead'),
            ),
          ],

          const SizedBox(height: 32),

          // Verification code input
          Text(
            'Enter Verification Code',
            style: FlutterFlowTheme.of(context).title3,
          ),
          const SizedBox(height: 16),

          PinCodeTextField(
            appContext: context,
            length: 6,
            onChanged: (value) => _verificationCode = value,
            onCompleted: (value) => _verificationCode = value,
            keyboardType: TextInputType.number,
            pinTheme: PinTheme(
              shape: PinCodeFieldShape.box,
              borderRadius: BorderRadius.circular(8),
              fieldHeight: 50,
              fieldWidth: 40,
              activeFillColor: Colors.white,
              inactiveFillColor: Colors.grey.shade100,
              selectedFillColor: Colors.white,
              activeColor: FlutterFlowTheme.of(context).primaryColor,
              inactiveColor: Colors.grey.shade300,
              selectedColor: FlutterFlowTheme.of(context).primaryColor,
            ),
            enableActiveFill: true,
          ),
          const SizedBox(height: 24),

          // Verify button
          SizedBox(
            width: double.infinity,
            child: FFButtonWidget(
              onPressed: _setupState.isLoading ? null : _verifyAndEnable2FA,
              text: _setupState.isLoading
                  ? 'Verifying...'
                  : 'Verify & Enable 2FA',
              options: FFButtonOptions(
                height: 50,
                color: FlutterFlowTheme.of(context).primaryColor,
                textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                      fontFamily: 'Readex Pro',
                      color: Colors.white,
                    ),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackupCodesStep() {
    final backupCodes = _setupState.backupCodes;
    if (backupCodes == null) return const SizedBox();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 64,
          ),
          const SizedBox(height: 16),

          Text(
            '2FA Enabled Successfully!',
            style: FlutterFlowTheme.of(context).title2.override(
                  fontFamily: 'Outfit',
                  color: Colors.green,
                ),
          ),
          const SizedBox(height: 24),

          Text(
            'Backup Codes',
            style: FlutterFlowTheme.of(context).title1,
          ),
          const SizedBox(height: 8),

          Text(
            'Save these backup codes in a secure location. You can use them to access your account if you lose your authenticator device.',
            style: FlutterFlowTheme.of(context).bodyText1,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Backup codes grid
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              children: [
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 3,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: backupCodes.length,
                  itemBuilder: (context, index) {
                    return Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Center(
                        child: Text(
                          TOTPUtils.formatBackupCode(backupCodes[index]),
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _copyToClipboard(backupCodes.join('\n')),
                  icon: const Icon(Icons.copy, size: 16),
                  label: const Text('Copy All Codes'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: FlutterFlowTheme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),

          // Complete button
          SizedBox(
            width: double.infinity,
            child: FFButtonWidget(
              onPressed: widget.onComplete,
              text: 'Complete Setup',
              options: FFButtonOptions(
                height: 50,
                color: Colors.green,
                textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                      fontFamily: 'Readex Pro',
                      color: Colors.white,
                    ),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}
