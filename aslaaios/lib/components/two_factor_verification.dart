// lib/components/two_factor_verification.dart

import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../models/two_factor_auth.dart';
import '../service/two_factor_service.dart';
import '../utils/totp_utils.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_widgets.dart';

class TwoFactorVerificationDialog extends StatefulWidget {
  final String phoneNumber;
  final Function(Map<String, dynamic>) onSuccess;
  final VoidCallback? onCancel;
  final String title;

  const TwoFactorVerificationDialog({
    Key? key,
    required this.phoneNumber,
    required this.onSuccess,
    this.onCancel,
    this.title = 'Two-Factor Authentication Required',
  }) : super(key: key);

  @override
  State<TwoFactorVerificationDialog> createState() =>
      _TwoFactorVerificationDialogState();
}

class _TwoFactorVerificationDialogState
    extends State<TwoFactorVerificationDialog> {
  final TwoFactorService _twoFactorService = TwoFactorService();
  final TextEditingController _backupCodeController = TextEditingController();

  String _verificationCode = '';
  bool _useBackupCode = false;
  bool _isLoading = false;
  String? _error;
  bool _showBackupCodeOption = false;

  @override
  void dispose() {
    _backupCodeController.dispose();
    super.dispose();
  }

  Future<void> _verifyCode() async {
    final code = _useBackupCode
        ? TOTPUtils.parseBackupCode(_backupCodeController.text)
        : _verificationCode;

    if (code.isEmpty) {
      setState(() {
        _error = 'Please enter a verification code';
      });
      return;
    }

    if (!_useBackupCode && !TOTPUtils.isValidTOTPFormat(code)) {
      setState(() {
        _error = 'Please enter a valid 6-digit code';
      });
      return;
    }

    if (_useBackupCode && !TOTPUtils.isValidBackupCodeFormat(code)) {
      setState(() {
        _error = 'Please enter a valid 8-character backup code';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final request = TwoFactorVerificationRequest(
        phoneNumber: widget.phoneNumber,
        token: code,
        isBackupCode: _useBackupCode,
      );

      final response = await _twoFactorService.verify2FA(request);

      if (response.success) {
        print('2FA_FLOW:: 2FA verification successful, completing login...');
        print('2FA_FLOW:: About to call completeLoginAfter2FA with phoneNumber: ${widget.phoneNumber}');

        // Complete login after 2FA verification
        final loginResponse = await _twoFactorService.completeLoginAfter2FA(
          widget.phoneNumber,
        );

        print('2FA_FLOW:: completeLoginAfter2FA call completed');

        print(
            '2FA_FLOW:: Complete login response success: ${loginResponse.success}');
        print('2FA_FLOW:: Complete login response data: ${loginResponse.data}');

        if (loginResponse.success && loginResponse.data != null) {
          print(
              '2FA_FLOW:: Calling onSuccess with data: ${loginResponse.data}');
          widget.onSuccess(loginResponse.data!);
        } else {
          print('2FA_FLOW:: Complete login failed: ${loginResponse.message}');
          setState(() {
            _error = loginResponse.message;
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          // Provide user-friendly error messages
          if (response.message.contains('already been used')) {
            _error =
                'This code has been used. Please wait for a new code (codes refresh every 30 seconds).';
          } else if (response.message.contains('Invalid token')) {
            _error =
                'Invalid code. Please check your authenticator app and try again.';
          } else {
            _error = response.message;
          }
          _isLoading = false;
        });
      }
    } catch (error) {
      setState(() {
        _error = 'Failed to verify code: $error';
        _isLoading = false;
      });
    }
  }

  void _toggleBackupCode() {
    setState(() {
      _useBackupCode = !_useBackupCode;
      _error = null;
      _verificationCode = '';
      _backupCodeController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        padding: const EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    Icons.security,
                    color: FlutterFlowTheme.of(context).primaryColor,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: FlutterFlowTheme.of(context).title2,
                    ),
                  ),
                  if (widget.onCancel != null)
                    IconButton(
                      onPressed: widget.onCancel,
                      icon: const Icon(Icons.close),
                    ),
                ],
              ),
              const SizedBox(height: 24),

              // Info message
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context)
                      .primaryColor
                      .withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: FlutterFlowTheme.of(context)
                        .primaryColor
                        .withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info,
                      color: FlutterFlowTheme.of(context).primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _useBackupCode
                            ? 'Enter one of your 8-character backup codes to continue.'
                            : 'Please enter the 6-digit code from your Google Authenticator app to continue.',
                        style: FlutterFlowTheme.of(context).bodyText1.override(
                              fontFamily: 'Readex Pro',
                              color: FlutterFlowTheme.of(context).primaryColor,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Input field
              if (!_useBackupCode) ...[
                Text(
                  'Verification Code',
                  style: FlutterFlowTheme.of(context).title3,
                ),
                const SizedBox(height: 16),
                PinCodeTextField(
                  appContext: context,
                  length: 6,
                  onChanged: (value) => _verificationCode = value,
                  onCompleted: (value) => _verificationCode = value,
                  keyboardType: TextInputType.number,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.box,
                    borderRadius: BorderRadius.circular(8),
                    fieldHeight: 50,
                    fieldWidth: 40,
                    activeFillColor: Colors.white,
                    inactiveFillColor: Colors.grey.shade100,
                    selectedFillColor: Colors.white,
                    activeColor: FlutterFlowTheme.of(context).primaryColor,
                    inactiveColor: Colors.grey.shade300,
                    selectedColor: FlutterFlowTheme.of(context).primaryColor,
                  ),
                  enableActiveFill: true,
                  onSubmitted: (_) => _verifyCode(),
                ),
              ] else ...[
                Text(
                  'Backup Code',
                  style: FlutterFlowTheme.of(context).title3,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _backupCodeController,
                  decoration: InputDecoration(
                    hintText: 'ABCD-1234',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: FlutterFlowTheme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                  ),
                  textCapitalization: TextCapitalization.characters,
                  onFieldSubmitted: (_) => _verifyCode(),
                ),
              ],

              const SizedBox(height: 16),

              // Toggle backup code option
              if (!_showBackupCodeOption && !_useBackupCode) ...[
                TextButton(
                  onPressed: () => setState(() => _showBackupCodeOption = true),
                  child: const Text('Having trouble? Use backup code'),
                ),
              ] else if (_showBackupCodeOption || _useBackupCode) ...[
                TextButton(
                  onPressed: _toggleBackupCode,
                  child: Text(
                    _useBackupCode
                        ? 'Use authenticator app instead'
                        : 'Use backup code instead',
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Error message
              if (_error != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error, color: Colors.red, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _error!,
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),

              // Verify button
              SizedBox(
                width: double.infinity,
                child: FFButtonWidget(
                  onPressed: _isLoading ? null : _verifyCode,
                  text: _isLoading ? 'Verifying...' : 'Verify',
                  options: FFButtonOptions(
                    height: 50,
                    color: FlutterFlowTheme.of(context).primaryColor,
                    textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                          fontFamily: 'Readex Pro',
                          color: Colors.white,
                        ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Help text
              Text(
                'If you\'ve lost access to your authenticator app, use one of your backup codes to sign in.',
                style: FlutterFlowTheme.of(context).bodyText2.override(
                      fontFamily: 'Readex Pro',
                      color: Colors.grey.shade600,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
