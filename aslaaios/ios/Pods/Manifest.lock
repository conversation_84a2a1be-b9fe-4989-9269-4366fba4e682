PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - firebase_core (3.15.1):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_messaging (15.2.9):
    - Firebase/Messaging (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_blue_plus_darwin (0.0.2):
    - Flutter
    - FlutterMacOS
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (3.3.1):
    - Flutter
  - flutter_sound (9.16.3):
    - Flutter
    - flutter_sound_core (= 9.16.3)
  - flutter_sound_core (9.16.3)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (6.1.0):
    - GoogleMaps (~> 9.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (9.4.0):
    - GoogleMaps/Maps (= 9.4.0)
  - GoogleMaps/Maps (9.4.0)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - mobile_scanner (6.0.2):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 7.0.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - Protobuf (3.29.5)
  - reactive_ble_mobile (0.0.1):
    - Flutter
    - FlutterMacOS
    - Protobuf (~> 3.5)
    - SwiftProtobuf (~> 1.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftProtobuf (1.30.0)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/darwin`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus_darwin (from `.symlinks/plugins/flutter_blue_plus_darwin/darwin`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - reactive_ble_mobile (from `.symlinks/plugins/reactive_ble_mobile/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - flutter_sound_core
    - Google-Maps-iOS-Utils
    - GoogleDataTransport
    - GoogleMaps
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC
    - Protobuf
    - SwiftProtobuf

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/darwin"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus_darwin:
    :path: ".symlinks/plugins/flutter_blue_plus_darwin/darwin"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  reactive_ble_mobile:
    :path: ".symlinks/plugins/reactive_ble_mobile/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 4f9ca89d92d3d21cec7ec580e78ca888e5fb68bd
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_core: ece862f94b2bc72ee0edbeec7ab5c7cb09fe1ab5
  firebase_messaging: e1a5fae495603115be1d0183bc849da748734e2b
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue_plus_darwin: 20a08bfeaa0f7804d524858d3d8744bcc1b6dbc3
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 50035aef357c5a8bdd67fd6bc81370d46efc4d16
  flutter_sound: 859f6ab6196dca46fb9b6ade22a1cdd465364afa
  flutter_sound_core: 2c5d7e93183845b53a776d99c4d6ebc2933ec495
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  Google-Maps-iOS-Utils: 0a484b05ed21d88c9f9ebbacb007956edd508a96
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 0608099d4870cac8754bdba9b6953db543432438
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  mobile_scanner: af8f71879eaba2bbcb4d86c6a462c3c0e7f23036
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  Protobuf: 164aea2ae380c3951abdc3e195220c01d17400e0
  reactive_ble_mobile: ea97709e18fa6f0b0592909725d7ae7e1c595b64
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftProtobuf: 3697407f0d5b23bedeba9c2eaaf3ec6fdff69349
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: a68af1771f93c5030574794d9e964be5c79e3b60

COCOAPODS: 1.16.2
