/* Copyright (c) 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "GMUGroundOverlay.h"

@implementation GMUGroundOverlay

@synthesize type = _type;

- (instancetype)initWithCoordinate:(CLLocationCoordinate2D)northEast
                         southWest:(CLLocationCoordinate2D)southWest
                            zIndex:(int)zIndex
                          rotation:(double)rotation
                              href:(NSString *)href {
  if (self = [super init]) {
    _type = @"GroundOverlay";
    _northEast = northEast;
    _southWest = southWest;
    _zIndex = zIndex;
    _rotation = rotation;
    _href = href;
  }
  return self;
}

@end
