ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
EXCLUDED_ARCHS[sdk=iphonesimulator*] = arm64
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher" "${PODS_CONFIGURATION_BUILD_DIR}/Google-Maps-iOS-Utils" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleToolboxForMac" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "${PODS_CONFIGURATION_BUILD_DIR}/Protobuf" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftProtobuf" "${PODS_CONFIGURATION_BUILD_DIR}/audioplayers_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus" "${PODS_CONFIGURATION_BUILD_DIR}/firebase_core" "${PODS_CONFIGURATION_BUILD_DIR}/firebase_messaging" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_blue_plus_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_sound" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_sound_core" "${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple" "${PODS_CONFIGURATION_BUILD_DIR}/google_maps_flutter_ios" "${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios" "${PODS_CONFIGURATION_BUILD_DIR}/mobile_scanner" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "${PODS_CONFIGURATION_BUILD_DIR}/reactive_ble_mobile" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios" "${PODS_ROOT}/GoogleMaps/Maps/Frameworks" "${PODS_ROOT}/MLImage/Frameworks" "${PODS_ROOT}/MLKitBarcodeScanning/Frameworks" "${PODS_ROOT}/MLKitCommon/Frameworks" "${PODS_ROOT}/MLKitVision/Frameworks" "${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleMaps/Maps"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) GPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1 $(inherited) PB_FIELD_32BIT=1 PB_NO_PACKED_STRUCTS=1 PB_ENABLE_MALLOC=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher/GTMSessionFetcher.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Google-Maps-iOS-Utils/GoogleMapsUtils.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleToolboxForMac/GoogleToolboxForMac.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/Protobuf/Protobuf.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/SwiftProtobuf/SwiftProtobuf.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/audioplayers_darwin/audioplayers_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus/connectivity_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/firebase_core/firebase_core.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/firebase_messaging/firebase_messaging.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_blue_plus_darwin/flutter_blue_plus_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications/flutter_local_notifications.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage/flutter_secure_storage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_sound/flutter_sound.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_sound_core/flutter_sound_core.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple/geolocator_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/google_maps_flutter_ios/google_maps_flutter_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios/image_picker_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/mobile_scanner/mobile_scanner.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple/permission_handler_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/reactive_ble_mobile/reactive_ble_mobile.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin/sqflite_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios/url_launcher_ios.framework/Headers" "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/Firebase" "${PODS_ROOT}/Headers/Public/GoogleMLKit" "${PODS_ROOT}/Headers/Public/GoogleMaps" $(inherited) ${PODS_ROOT}/Firebase/CoreOnly/Sources $(inherited) ${PODS_ROOT}/GoogleMLKit/MLKitCore/Sources
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift $(inherited) $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift
OTHER_LDFLAGS = $(inherited) -ObjC -l"c++" -l"sqlite3" -l"z" -framework "AVFoundation" -framework "Accelerate" -framework "Contacts" -framework "CoreBluetooth" -framework "CoreData" -framework "CoreGraphics" -framework "CoreImage" -framework "CoreLocation" -framework "CoreMedia" -framework "CoreTelephony" -framework "CoreText" -framework "CoreVideo" -framework "FBLPromises" -framework "FirebaseCore" -framework "FirebaseCoreInternal" -framework "FirebaseInstallations" -framework "FirebaseMessaging" -framework "Foundation" -framework "GLKit" -framework "GTMSessionFetcher" -framework "GoogleDataTransport" -framework "GoogleMaps" -framework "GoogleMapsUtils" -framework "GoogleToolboxForMac" -framework "GoogleUtilities" -framework "ImageIO" -framework "LocalAuthentication" -framework "MLImage" -framework "MLKitBarcodeScanning" -framework "MLKitCommon" -framework "MLKitVision" -framework "MediaPlayer" -framework "Metal" -framework "OpenGLES" -framework "Protobuf" -framework "QuartzCore" -framework "Security" -framework "SwiftProtobuf" -framework "SystemConfiguration" -framework "UIKit" -framework "audioplayers_darwin" -framework "connectivity_plus" -framework "firebase_core" -framework "firebase_messaging" -framework "flutter_blue_plus_darwin" -framework "flutter_local_notifications" -framework "flutter_secure_storage" -framework "flutter_sound" -framework "flutter_sound_core" -framework "geolocator_apple" -framework "google_maps_flutter_ios" -framework "image_picker_ios" -framework "mobile_scanner" -framework "nanopb" -framework "path_provider_foundation" -framework "permission_handler_apple" -framework "reactive_ble_mobile" -framework "shared_preferences_foundation" -framework "sqflite_darwin" -framework "url_launcher_ios" -weak_framework "UserNotifications"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/Firebase" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations" "-F${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging" "-F${PODS_CONFIGURATION_BUILD_DIR}/Flutter" "-F${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher" "-F${PODS_CONFIGURATION_BUILD_DIR}/Google-Maps-iOS-Utils" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleMLKit" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleMaps" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleToolboxForMac" "-F${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities" "-F${PODS_CONFIGURATION_BUILD_DIR}/MLImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/MLKitBarcodeScanning" "-F${PODS_CONFIGURATION_BUILD_DIR}/MLKitCommon" "-F${PODS_CONFIGURATION_BUILD_DIR}/MLKitVision" "-F${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC" "-F${PODS_CONFIGURATION_BUILD_DIR}/Protobuf" "-F${PODS_CONFIGURATION_BUILD_DIR}/SwiftProtobuf" "-F${PODS_CONFIGURATION_BUILD_DIR}/audioplayers_darwin" "-F${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/firebase_core" "-F${PODS_CONFIGURATION_BUILD_DIR}/firebase_messaging" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_blue_plus_darwin" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_sound" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_sound_core" "-F${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple" "-F${PODS_CONFIGURATION_BUILD_DIR}/google_maps_flutter_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/mobile_scanner" "-F${PODS_CONFIGURATION_BUILD_DIR}/nanopb" "-F${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "-F${PODS_CONFIGURATION_BUILD_DIR}/reactive_ble_mobile" "-F${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "-F${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
